#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-27 15:43+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"Language: fr_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base
#: model:ir.module.module,description:base.module_account_inter_company_rules
msgid ""
" Module for synchronization of Documents between several companies. For example, this allow you to have a Sales Order created automatically when a Purchase Order is validated with another company of the system as vendor, and inversely.\n"
"\n"
"    Supported documents are invoices/credit notes.\n"
msgstr ""
"Module de synchronisation de documents entre plusieurs entreprises. Par exemple, cela permet de créer automatiquement une commande client lorsqu'une commande fournisseur est validée avec une autre entreprise fournisseur du système, et inversement.\n"
"\n"
"    Les documents pris en charge sont les factures/notes de crédit.\n"
