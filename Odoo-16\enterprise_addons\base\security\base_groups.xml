<?xml version="1.0"?>
<odoo>
    <data>

        <!--
            Users Groups
            Note that the field 'category_id' is set later in
            base/data/ir_module_category_data.xml
        -->
        <record model="res.groups" id="group_erp_manager">
            <field name="name">Access Rights</field>
            <field name="implied_ids" eval="[Command.link(ref('group_user'))]"/>
        </record>

        <record id="group_sanitize_override" model="res.groups">
            <field name="name">Bypass HTML Field Sanitize</field>
        </record>

        <record model="res.groups" id="group_system">
            <field name="name">Settings</field>
            <field name="implied_ids" eval="[Command.link(ref('group_erp_manager')), Command.link(ref('group_sanitize_override'))]"/>
            <field name="users" eval="[Command.link(ref('base.user_root')), Command.link(ref('base.user_admin'))]"/>
        </record>

        <record model="res.groups" id="group_user">
            <field name="name">Internal User</field>
        </record>

        <record id="default_user" model="res.users">
            <field name="groups_id" eval="[Command.link(ref('base.group_user'))]"/>
        </record>

        <record model="res.groups" id="group_multi_company">
            <field name="name">Multi Companies</field>
        </record>

        <record model="res.groups" id="group_multi_currency">
            <field name="name">Multi Currencies</field>
        </record>

        <record model="res.groups" id="group_no_one">
            <field name="name">Technical Features</field>
        </record>
        <record id="group_allow_export" model="res.groups">
            <field name="name">Access to export feature</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="users" eval="[Command.link(ref('base.user_root')), Command.link(ref('base.user_admin'))]"/>
        </record>
        <record model="res.groups" id="group_user">
            <field name="implied_ids" eval="[Command.link(ref('group_no_one'))]"/>
            <field name="users" eval="[Command.link(ref('base.user_root')), Command.link(ref('base.user_admin'))]"/>
        </record>

        <record model="res.groups" id="group_partner_manager">
            <field name="name">Contact Creation</field>
            <field name="users" eval="[Command.link(ref('base.user_root')), Command.link(ref('base.user_admin'))]"/>
        </record>

        <record id="default_user" model="res.users">
            <field name="groups_id" eval="[Command.link(ref('base.group_partner_manager')), Command.link(ref('base.group_allow_export'))]"/>
        </record>

        <!--
            A group dedicated to the portal users, making groups
            restrictions more convenient.
        -->
        <record id="group_portal" model="res.groups">
            <field name="name">Portal</field>
            <field name="comment">Portal members have specific access rights (such as record rules and restricted menus).
                They usually do not belong to the usual Odoo groups.</field>
        </record>
        <!--
            A group dedicated to the public user only, making groups
            restrictions more convenient.
        -->
        <record id="group_public" model="res.groups">
            <field name="name">Public</field>
            <field name="comment">Public users have specific access rights (such as record rules and restricted menus).
                They usually do not belong to the usual Odoo groups.</field>
        </record>

        <record id="public_user" model="res.users">
            <field name="groups_id" eval="[Command.link(ref('base.group_public'))]"/>
        </record>

        <!-- Default template user for new users signing in -->
        <record id="template_portal_user_id" model="res.users">
            <field name="name">Portal User Template</field>
            <field name="login">portaltemplate</field>
            <field name="active" eval="False"/>
            <field name="groups_id" eval="[Command.set([ref('base.group_portal')])]"/>
            <field name="signature" /> <!-- Needed for avoiding the _compute_signature triggering on each update -->
        </record>

        <record id="default_template_user_config" model="ir.config_parameter">
            <field name="key">base.template_portal_user_id</field>
            <field name="value" ref="template_portal_user_id"/>
        </record>

    </data>
</odoo>
