# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_address_extended
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <a<PERSON><PERSON>@allegro.lv>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.2\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-11 14:34+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: base_address_extended
#: model:ir.model.fields,help:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_users__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"Atzīmējiet šo izvēles rūtiņu, lai pārliecinātos, ka katrai šajā valstī "
"izveidotajai adresei valsts pilsētu sarakstā ir izvēlēts \"Pilsēta\"."

#. module: base_address_extended
#: model:ir.actions.act_window,name:base_address_extended.action_res_city_tree
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_country_city_extended_form
msgid "Cities"
msgstr "Pilsētas"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_city
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_city_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_tree
msgid "City"
msgstr "Pilsēta"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__city_id
msgid "City ID"
msgstr "Pilsētas ID"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_partner
msgid "Contact"
msgstr "Kontakts"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_country
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__country_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Country"
msgstr "Valsts"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__display_name
msgid "Display Name"
msgstr "Parādīt vārdu"

#. module: base_address_extended
#: model_terms:ir.actions.act_window,help:base_address_extended.action_res_city_tree
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"                your partner records. Note that an option can be set on each country separately\n"
"                to enforce any address of it to have a city in this list."
msgstr ""
"Uzrādiet un pārvaldiet visu pilsētu sarakstu, ko var piešķirt\n"
"                Jūsu partnera ierakstos. Ņemiet vērā, šī opcija var tikt iestatīta katrai valstij atsevišķi,\n"
"                lai nodrošinātu, ka jebkurai adresei jābūt pilsētai no šī saraksta."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number2
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number2
msgid "Door"
msgstr "Durvis"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Door #"
msgstr "Durvju #"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__country_enforce_cities
msgid "Enforce Cities"
msgstr "Ieviest pilsētas"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number
msgid "House"
msgstr "Māja"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "House #"
msgstr "Mājas #"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__id
msgid "ID"
msgstr "ID"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz mainīts"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_uid
msgid "Last Updated by"
msgstr "Pēdējoreiz atjaunoja"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_date
msgid "Last Updated on"
msgstr "Pēdējoreiz atjaunots"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__name
msgid "Name"
msgstr "Nosaukums"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
msgid "Search City"
msgstr "Meklēt pilsētu"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__state_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "State"
msgstr "Posmi"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street"
msgstr "Iela"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street 2..."
msgstr "Iela 2..."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_name
msgid "Street Name"
msgstr "Ielas nosaukums"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street..."
msgstr "Iela..."

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "ZIP"
msgstr "Pasta indekss"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__zipcode
msgid "Zip"
msgstr "Zip"
