# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# Lux <PERSON>k <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Lux Sok <<EMAIL>>, 2023\n"
"Language-Team: Khmer (https://app.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid " '*' is not a valid Regex Barcode Pattern. Did you mean '.*' ?"
msgstr "មិនមែនជាលំនាំដើមកូដ Regex Barcode ត្រឹមត្រូវទេ។ តើអ្នកចង់មានន័យ"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"<i>Barcodes Nomenclatures</i> define how barcodes are recognized and categorized.\n"
"                                When a barcode is scanned it is associated to the <i>first</i> rule with a matching\n"
"                                pattern. The pattern syntax is that of regular expression, and a barcode is matched\n"
"                                if the regular expression matches a prefix of the barcode."
msgstr ""
"<i>លំដាប់ឈ្មោះកូដសម្ងាត់កំណត់របៀបដែលកូដសម្ងាត់ត្រូវបានទទួលស្គាល់និងចាត់ថ្នាក់។"
"   </i> "
"                             នៅពេលស្កេនកូដត្រូវបានស្កេនវាត្រូវបានភ្ជាប់ជាមួយក្បួនដំបូងដែលផ្គូរផ្គង"
"                                 <i>នាំ។ "
"វាក្យសម្ព័ន្ធលំនាំ</i>ដើមគឺកន្សោមធម្មតានិងលេខកូដត្រូវបានផ្គូផ្គង "
"                                ប្រសិនបើកន្សោមធម្មតាផ្គូផ្គងបុព្វបទនៃកូដបាកូដ។"

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid ""
"A barcode nomenclature defines how the point of sale identify and interprets"
" barcodes"
msgstr "តារាងឈ្មោះបារកូដកំណត់របៀបដែលចំណុចនៃការលក់កំណត់និងបកប្រែលេខកូដ"

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid "Add a new barcode nomenclature"
msgstr "បន្ថែមតារាងនាមលេខកូដថ្មី"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__alias
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__alias
msgid "Alias"
msgstr "ក្លែងក្លាយ"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__always
msgid "Always"
msgstr "តែងតែ"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__name
msgid "An internal identification for this barcode nomenclature rule"
msgstr "ការកំណត់អត្តសញ្ញាណផ្ទៃក្នុងសម្រាប់ក្បួនតនានាមលេខកូដ"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__name
msgid "An internal identification of the barcode nomenclature"
msgstr "ការកំណត់អត្តសញ្ញាណផ្ទៃក្នុងនៃតារាងនាមកូដកម្ម"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__any
msgid "Any"
msgstr "បន្ថែមទៀត"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcodes_barcode_events_mixin
msgid "Barcode Event Mixin"
msgstr "ព្រឹត្តិការណ៍ Barcode Mixin"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_nomenclature
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Barcode Nomenclature"
msgstr "តារាងលេខកូដ"

#. module: barcodes
#: model:ir.actions.act_window,name:barcodes.action_barcode_nomenclature_form
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_tree
msgid "Barcode Nomenclatures"
msgstr "តារាងកូដ"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__pattern
msgid "Barcode Pattern"
msgstr "លំនាំលេខកូដ"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_rule
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_rule_form
msgid "Barcode Rule"
msgstr "លក្ខណលេខកូដ"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Barcode Scanned"
msgstr "បារកូដបានស្កេន"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
#, python-format
msgid "Barcode: "
msgstr ""

#. module: barcodes
#: model:ir.model,name:barcodes.model_res_company
msgid "Companies"
msgstr "ក្រុមហ៊ុន"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__display_name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean13
msgid "EAN-13"
msgstr "EAN-13"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__ean2upc
msgid "EAN-13 to UPC-A"
msgstr "EAN-13 to UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean8
msgid "EAN-8"
msgstr "EAN-8"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__encoding
msgid "Encoding"
msgstr "ការអ៊ិនកូដ"

#. module: barcodes
#: model:ir.model,name:barcodes.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP ជុំវិញ"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__id
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__id
msgid "ID"
msgstr "អត្តសញ្ញាណ"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature____last_update
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__none
msgid "Never"
msgstr "មិននែរ"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_res_company__nomenclature_id
msgid "Nomenclature"
msgstr "កត្តានាមត្រកូល"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"Patterns can also define how numerical values, such as weight or price, can be\n"
"                                encoded into the barcode. They are indicated by <code>{NNN}</code> where the N's\n"
"                                define where the number's digits are encoded. Floats are also supported with the \n"
"                                decimals indicated with D's, such as <code>{NNNDD}</code>. In these cases, \n"
"                                the barcode field on the associated records <i>must</i> show these digits as \n"
"                                zeroes."
msgstr ""
"លំនាំក៏អាចកំណត់ពីរបៀបដែលតម្លៃជាលេខដូចជាទម្ងន់ឬតម្លៃអាចជា "
"                                បានអ៊ិនកូដចូលទៅក្នុងលេខកូដ។ "
"ពួកវាត្រូវបានបង្ហាញដោយ {NNN} ដែលជាកន្លែងរបស់ N "
"                                កំណត់កន្លែងដែលលេខរបស់លេខត្រូវបានអ៊ិនកូដ។ "
"floats ក៏ត្រូវបានគាំទ្រដោយ "
"                                ទសភាគបង្ហាញដោយដឺដូចជា {NNNDD} ។ "
"នៅក្នុងករណីទាំងនេះ, "
"                                វាលលេខកូដឌីជីថលនៅលើកំណត់ត្រាដែលពាក់ព័ន្ធត្រូវបង្ហាញតួលេខទាំងនេះជា"
"                                 សូន្យ។"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__name
msgid "Rule Name"
msgstr "ឈ្មោះច្បាប់"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__rule_ids
msgid "Rules"
msgstr "ច្បាប់"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__sequence
msgid "Sequence"
msgstr "លំដាប់"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Tables"
msgstr "តារាង"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__pattern
msgid "The barcode matching pattern"
msgstr "គំរូផ្គូផ្គងលេខកូដ"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"The barcode pattern %(pattern)s does not lead to a valid regular expression."
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__rule_ids
msgid "The list of barcode rules"
msgstr "បញ្ជីនៃច្បាប់កូដសៀគ្វី"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__alias
msgid "The matched pattern will alias to this barcode"
msgstr "លំនាំដែលផ្គូផ្គងនឹងឈ្មោះក្លែងក្លាយទៅកូដនេះ"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: a rule can only "
"contain one pair of braces."
msgstr ""

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: braces can only "
"contain N's followed by D's."
msgstr ""

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: empty braces."
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr ""
"ច្បាប់នេះនឹងអនុវត្តបានតែក្នុងករណីដែលកូដត្រូវបានអ៊ិនកូដជាមួយការអ៊ិនកូដដែលបានបញ្ជាក់"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__type
msgid "Type"
msgstr "ប្រភេទ"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid ""
"UPC Codes can be converted to EAN by prefixing them with a zero. This "
"setting determines if a UPC/EAN barcode should be automatically converted in"
" one way or another when trying to match a rule with the other encoding."
msgstr ""
"លេខកូដ UPC អាចត្រូវបានបម្លែងទៅ EAN ដោយដាក់បុព្វបទដោយលេខសូន្យ។ "
"ការកំណត់នេះកំណត់ថាតើកូដ bar UPC / EAN "
"គួរត្រូវបានបម្លែងដោយស្វ័យប្រវត្តិក្នុងវិធីមួយឬវិធីផ្សេងនៅពេលដែលផ្គូផ្គងក្បួនដោយការអ៊ិនកូដផ្សេងទៀត។"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__upca
msgid "UPC-A"
msgstr "UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__upc2ean
msgid "UPC-A to EAN-13"
msgstr "UPC-A to EAN-13"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid "UPC/EAN Conversion"
msgstr "ការផ្លាស់ប្តូរ UPC / EAN"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__product
msgid "Unit Product"
msgstr "ផលិតផលឯកតា"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
#, python-format
msgid "Unknown barcode command"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__sequence
msgid ""
"Used to order rules such that rules with a smaller sequence match first"
msgstr ""
"ត្រូវបានប្រើដើម្បីបញ្ជាក្បួនច្បាប់បែបនោះដែលផ្គូរផ្គងជាមួយការផ្គូផ្គងលំដាប់តូចជាងមុន"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "តម្លៃនៃកូដឌីតចុងក្រោយត្រូវបានស្កេន។"
