# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# Lux <PERSON>k <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Lux Sok <<EMAIL>>, 2023\n"
"Language-Team: Khmer (https://app.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"\"\n"
"                (ID:"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__help
msgid "Action Description"
msgstr "ការពិពណ៌នាសកម្មភាព"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Action Name"
msgstr "ឈ្មោះសកម្មភាព"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__state
msgid "Action To Do"
msgstr "តាមដានសកម្មភាព"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__type
msgid "Action Type"
msgstr "ប្រភេទសកម្មភាព"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "សកម្ម"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_type_id
msgid "Activity"
msgstr "សកម្មភាព"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_type
msgid "Activity User Type"
msgstr "ប្រភេទសកម្មភាពត្រូវប្រើប្រាស់"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__partner_ids
msgid "Add Followers"
msgstr "បន្ថែមការតាមដាន"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "អនុវត្តលើ"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "ទុកជាឯកសារ"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
msgid "Automated Action"
msgstr "សកម្មភាពស័យប្រវត្ត"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
msgid "Automated Actions"
msgstr "សម្មភាពស្វ័យប្រវត្ត"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation"
msgstr "ស្វ័យប្រវត្តិកម្ម"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
#: model:ir.cron,cron_name:base_automation.ir_cron_data_base_automation_check
msgid "Base Action Rule: check and execute"
msgstr "វិធានសកម្មភាពមូលដ្ឋាន: ពិនិត្យនិងអនុវត្ត"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "Based on Form Modification"
msgstr "ដោយផ្អែកលើការកែប្រែទម្រង់"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on Timed Condition"
msgstr "ដោយផ្អែកលើពេលវេលាកំណត់"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "មុនពេលធ្វើឱ្យទាន់សម័យដែន"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_model_id
msgid "Binding Model"
msgstr "គំរូចង"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_type
msgid "Binding Type"
msgstr "ប្រភេទគំរូ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_view_types
msgid "Binding View Types"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__child_ids
msgid "Child Actions"
msgstr "សកម្មភាពកេ្មង"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__child_ids
msgid ""
"Child server actions that will be executed. Note that the last return "
"returned action value will be used as global return value."
msgstr ""
"សកម្មភាពម៉ាស៊ីនមេកុមារដែលនឹងត្រូវបានប្រតិបត្តិ។ "
"ចំណាំថាការត្រឡប់មកវិញត្រឡប់មកវិញចុងក្រោយតម្លៃសកម្មភាពនឹងត្រូវបានប្រើជាតម្លៃត្រឡប់សកល។"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__mail_post_method
msgid ""
"Choose method for email sending:\n"
"EMail: send directly emails\n"
"Post as Message: post on document and notify followers\n"
"Post as Note: log a note on document"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
msgid "Days"
msgstr "ថ្ងៃ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date.\n"
"        You can put a negative number if you need a delay before the\n"
"        trigger date, like sending a reminder 15 minutes before a meeting."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "ការពន្យារពេលបន្ទាប់ពីកាលបរិច្ឆេទ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "ប្រភេទការពន្យារពេល"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Disable Action"
msgstr ""

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"Disabling this automated action will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range
msgid "Due Date In"
msgstr "កាលបរិច្ឆេទខាងក្នុង"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range_type
msgid "Due type"
msgstr "ប្រភេទត្រឹមត្រូវ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Edit action"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__template_id
msgid "Email Template"
msgstr "គំរូអ៊ីម៉ែល"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Email, followers or activities action types cannot be used when deleting "
"records."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__xml_id
msgid "External ID"
msgstr "អត្តលេខខាងក្រៅ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr ""

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Form Modification based actions can only be used with code action type."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__groups_id
msgid "Groups"
msgstr ""

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "ម៉ោង"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "អត្តសញ្ញាណ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "អត្តសញ្ញានសកម្មភាពប្រសិនប្រើវានៅក្នុងឯកសារXML"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the action "
"rule."
msgstr "ប្រសិនបើមានវត្តមានលក្ខខណ្ឌនេះត្រូវតែពេញចិត្តមុនពេលអនុវត្តវិធានការ។"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record."
msgstr ""
"ប្រសិនបើមានវត្តមានលក្ខខណ្ឌនេះត្រូវតែពេញចិត្តមុនពេលធ្វើបច្ចុប្បន្នភាពកំណត់ត្រា។"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "ដំណើរការចុងក្រោយ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__link_field_id
msgid "Link Field"
msgstr ""

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "កំណត់ហេតុ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "ម៉ូដែល"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "ឈ្មោះម៉ូដែល"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__crud_model_id
msgid ""
"Model for record creation / update. Set this field only to specify a "
"different model than the base model."
msgstr ""
"គំរូសម្រាប់បង្កើតកំណត់ត្រា / ធ្វើបច្ចុប្បន្នភាព។ "
"កំណត់វាលនេះតែដើម្បីបញ្ជាក់ម៉ូដែលខុសគ្នាជាងគំរូមូលដ្ឋាន។"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the server action runs."
msgstr "ម៉ូដែលដែលសកម្មភាពម៉ាស៊ីនមេដំណើរការ។"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
msgid "Months"
msgstr "ខែ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_note
msgid "Note"
msgstr "កំណត់សម្គាល់"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Note that this action can be triggered up to %d minutes after its schedule."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "នៅលើការផ្លាស់ប្តូរវាលការកេះ"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On Creation"
msgstr "ការបង្កើតលើ"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On Creation & Update"
msgstr "ការបង្កើតទៅលើ និង​ការធ្វើបច្ចប្បន្នភាព"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On Deletion"
msgstr "ការលុបទៅលើ"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On Update"
msgstr "ការធ្វើបច្ចុប្បន្នភាព"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__help
msgid ""
"Optional help text for the users with a description of the target view, such"
" as its usage and purpose."
msgstr ""
"អត្ថបទជំនួយស្រេចចិត្តសម្រាប់អ្នកប្រើដែលមានការពិពណ៌នាអំពីទិដ្ឋភាពគោលដៅដូចជាការប្រើប្រាស់និងគោលបំណងរបស់វា។"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__link_field_id
msgid ""
"Provide the field used to link the newly created record on the record used "
"by the server action."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__code
msgid "Python Code"
msgstr "កូដភូតុន"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_id
msgid "Responsible"
msgstr "ការទទួលខុសត្រូវ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__mail_post_method
msgid "Send as"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__sequence
msgid "Sequence"
msgstr "លំដាប់"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "សកម្មភាពម៉ាស៊ីនបម្រើ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_id
msgid "Server Actions"
msgstr "សកម្មភាពប្រើ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__binding_model_id
msgid ""
"Setting a value makes this action available in the sidebar for the given "
"model."
msgstr ""
"ការកំណត់តម្លៃធ្វើឱ្យសកម្មភាពនេះមាននៅក្នុងរបារចំហៀងសម្រាប់ម៉ូដែលដែលបានផ្តល់ឱ្យ។"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Setup a new automated automation"
msgstr "រៀបចំស្វ័យប្រវត្តិកម្មស្វ័យប្រវត្តិថ្មី"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_summary
msgid "Summary"
msgstr "សង្ខេប"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_id
msgid "Target Model"
msgstr "ម៉ូដែលក្រុមគោលដៅ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_name
msgid "Target Model Name"
msgstr ""

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"%(trigger_value)s\"\" "
"%(trigger_label)sអាចត្រូវបានប្រើតែ%(state_value)sជាមួយប្រភេទអំពើប៉ុណ្ណោះ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The action will be triggered if and only if one of these fields is updated. "
"If empty, all fields are watched."
msgstr ""

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"The error occurred during the execution of the automated action\n"
"                \""
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "កេះ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "កាលបរិច្ឆេទលកខណ្ឌន្ទិកៈ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Trigger Fields"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create a new Record': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)\n"
"- 'Send SMS Text Message': send SMS, log them on documents (SMS)"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "ការប្រើប្រាស់"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""
"ប្រើ 'អ្នកប្រើជាក់លាក់' "
"ដើម្បីកំណត់អ្នកប្រើដូចគ្នានៅលើសកម្មភាពបន្ទាប់ជានិច្ច។ ប្រើ "
"'អ្នកប្រើទូទៅពីកំណត់ត្រា' "
"ដើម្បីបញ្ជាក់ឈ្មោះវាលរបស់អ្នកប្រើដើម្បីជ្រើសលើកំណត់ត្រា។"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "ការប្រើប្រាស់ប្រទិតិន"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Use automated actions to automatically trigger actions for\n"
"                various screens. Example: a lead created by a specific user may\n"
"                be automatically set to a specific Sales Team, or an\n"
"                opportunity which still has status pending after 14 days might\n"
"                trigger an automatic reminder email."
msgstr ""
"ប្រើសកម្មភាពដោយស្វ័យប្រវត្តិដើម្បីកេះសកម្មភាពដោយស្វ័យប្រវត្តិ\n"
"                អេក្រង់ជាច្រើន។ ឧទាហរណ៍: ការនាំមុខដែលបានបង្កើតឡើងដោយអ្នកប្រើជាក់លាក់អាច\n"
"                ត្រូវបានកំណត់ដោយស្វ័យប្រវត្តិទៅក្រុមលក់ជាក់លាក់ឬមួយ\n"
"                ឱកាសដែលនៅតែមានស្ថានភាពស្ថិតក្នុងការរង់ចាំបន្ទាប់ពី 14 ថ្ងៃអាច\n"
"                កេះអ៊ីមែលរំលឹកស្វ័យប្រវត្តិ។"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_field_name
msgid "User field name"
msgstr "ឈ្មោះការប្រើប្រាស់ឯកសារ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__fields_lines
msgid "Value Mapping"
msgstr "គុណតំលៃតំបង់គោលដៅ"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Warning"
msgstr "ព្រមាន"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possible to use a "
"calendar to compute the date based on working days."
msgstr ""
"នៅពេលដែលគណនាពេលវេលាដែលមានមូលដ្ឋានលើពេលវេលាវាអាចប្រើប្រតិទិនដើម្បីគណនាកាលបរិច្ឆេទដោយផ្អែកលើថ្ងៃធ្វើការ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__sequence
msgid ""
"When dealing with multiple actions, the execution order is based on the "
"sequence. Low number means high priority."
msgstr ""
"នៅពេលដោះស្រាយជាមួយសកម្មភាពច្រើនលំដាប់បញ្ជាត្រូវផ្អែកលើលំដាប់។ "
"លេខទូរស័ព្ទមានន័យថាអាទិភាពខ្ពស់។"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "នៅពេលដោះធីកច្បាប់នេះត្រូវបានលាក់ហើយនឹងមិនត្រូវបានប្រតិបត្តិទេ។"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__code
msgid ""
"Write Python code that the action will execute. Some variables are available"
" for use; help about python expression is given in the help tab."
msgstr ""
"សរសេរកូដ Python ថាសកម្មភាពនឹងត្រូវបានប្រតិបត្តិ។ "
"អថេរមួយចំនួនអាចរកបានសម្រាប់ប្រើប្រាស់។ ជំនួយអំពីកន្សោម python "
"ត្រូវបានផ្តល់ឱ្យក្នុងផ្ទាំងជំនួយ។"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"You can ask an administrator to disable or correct this automated action."
msgstr ""

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "You can disable this automated action or edit it to solve the issue."
msgstr ""

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
