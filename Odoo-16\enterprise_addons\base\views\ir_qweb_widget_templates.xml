<odoo>
<template id="contact_name">
    <div t-if="'name' in fields">
        <t t-if="object.name">
            <span itemprop="name" t-esc="name"/>
        </t>
        <t t-if="not object.name and object.parent_name">
            <span itemprop="name" t-esc="object.parent_name"/>
        </t>
        <t t-if="options.get('country_image') and 'country_id' in fields and object.country_id and object.country_id.image_url">
            <span t-field="object.country_id.image_url" t-options='{"widget": "image_url", "class": "country_flag"}'/>
        </t>
    </div>
</template>
<template id="contact">
    <address t-ignore="true" class="mb-0" itemscope="itemscope" itemtype="http://schema.org/Organization">
        <div t-if="not (('name' in fields) or (address and 'address' in fields) or (city and 'city' in fields) or (mobile and 'mobile' in fields) or (website and 'website' in fields) or (email and 'email' in fields))" class="css_non_editable_mode_hidden">
            --<span class="text-muted" t-esc="name"/>--
        </div>
        <t t-if="object.country_id.name_position != 'after'">
            <t t-call="base.contact_name"/>
        </t>
        <div itemprop="address" itemscope="itemscope" itemtype="http://schema.org/PostalAddress">
            <div t-if="address and 'address' in fields" class="d-flex align-items-baseline">
                <i t-if="not options.get('no_marker')" class="fa fa-map-marker fa-fw" role="img" aria-label="Address" title="Address"/>
                <span class="w-100 d-block" itemprop="streetAddress" t-esc="address"/>
            </div>
            <div t-if="city and 'city' in fields" class="d-flex align-items-baseline">
                <i t-if="not options.get('no_marker')" class="fa fa-map-marker fa-fw" role="img" aria-label="Address" title="Address"/>
                <span>
                    <div>
                        <span itemprop="addressLocality" t-esc="city"/>,
                        <span itemprop="addressCountry" t-esc="country_id"/>
                    </div>
                </span>
            </div>
            <div t-if="phone and 'phone' in fields"><i t-if="not options.get('no_marker') or options.get('phone_icons')" class='fa fa-phone fa-fw' role="img" aria-label="Phone" title="Phone"/> <span class="o_force_ltr" itemprop="telephone" t-esc="phone"/></div>
            <div t-if="mobile and 'mobile' in fields"><i t-if="not options.get('no_marker') or options.get('phone_icons')" class='fa fa-mobile fa-fw' role="img" aria-label="Mobile" title="Mobile"/> <span class="o_force_ltr" itemprop="telephone" t-esc="mobile"/></div>
            <!-- Prevent issue with iOS Safari parsing of schema data without telephone itemprops -->
            <div t-if="not (phone and 'phone' in fields) and not (mobile and 'mobile' in fields)" itemprop="telephone"/>
            <div t-if="website and 'website' in fields">
                <i t-if="not options.get('no_marker')" class='fa fa-globe' role="img" aria-label="Website" title="Website"/>
                <a t-att-href="website and '%s%s' % ('http://' if '://' not in website else '',website)"><span itemprop="website" t-esc="website"/></a>
            </div>
            <div t-if="email and 'email' in fields"><i t-if="not options.get('no_marker')" class='fa fa-envelope fa-fw' role="img" aria-label="Email" title="Email"/> <span itemprop="email" t-esc="email"/></div>
        </div>
        <div t-if="vat and 'vat' in fields"><span t-esc="vat_label"/>: <span itemprop="vatID" t-esc="vat"/></div>
        <t t-if="object.country_id and object.country_id.name_position == 'after'">
            <t t-call="base.contact_name"/>
        </t>
    </address>
</template>
</odoo>
