<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_base_module_update" model="ir.ui.view">
            <field name="name">Module Update</field>
            <field name="model">base.module.update</field>
            <field name="arch" type="xml">
                <form string="Update Module List">
                    <field name="state" invisible="1"/>
                    <separator string="Module Update Result" states="done"/>
                    <group states="init">
                        <span class="o_form_label" colspan="2">Click on Update below to start the process...</span>
                    </group>
                    <group states="done" >
                        <field name="updated"/>
                        <field name="added" />
                    </group>
                    <footer>
                        <div states="init">
                            <button name="update_module" string="Update" type="object" class="btn-primary"/>
                            <button special="cancel" data-hotkey="z" string="Cancel" class="btn-secondary"/>
                        </div>
                        <div states="done">
                            <button name="action_module_open" string="Open Apps" type="object" class="btn-primary" data-hotkey="q"/>
                            <button special="cancel" data-hotkey="z" string="Close" class="btn-secondary"/>
                        </div>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_view_base_module_update" model="ir.actions.act_window">
            <field name="name">Module Update</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">base.module.update</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <menuitem
            name="Update Apps List"
            action="action_view_base_module_update"
            id="menu_view_base_module_update"
            groups="base.group_no_one"
            parent="menu_management"
            sequence="40"/>

    </data>
</odoo>
