# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_action_rule
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-10-10 09:48+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Kazakh (http://www.transifex.com/odoo/odoo-9/language/kk/)\n"
"Language: kk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid "<b>Please choose the document type before setting the conditions.</b>"
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_tree
msgid "Action Rule"
msgstr ""

#. module: base_action_rule
#: model:ir.model,name:base_action_rule.model_base_action_rule_line_test
msgid "Action Rule Line Test"
msgstr ""

#. module: base_action_rule
#: model:ir.model,name:base_action_rule.model_base_action_rule_lead_test
msgid "Action Rule Test"
msgstr ""

#. module: base_action_rule
#: model:ir.model,name:base_action_rule.model_base_action_rule
msgid "Action Rules"
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid "Actions"
msgstr "Әрекеттер"

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_active
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_active
msgid "Active"
msgstr "Белсенді"

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_act_followers
msgid "Add Followers"
msgstr ""

#. module: base_action_rule
#: model:ir.actions.act_window,name:base_action_rule.base_action_rule_act
#: model:ir.ui.menu,name:base_action_rule.menu_base_action_rule_form
msgid "Automated Actions"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule,kind:0
msgid "Based on Form Modification"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule,kind:0
msgid "Based on Timed Condition"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_filter_pre_domain
msgid "Before Update Domain"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_filter_pre_id
msgid "Before Update Filter"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule.lead.test,state:0
msgid "Cancelled"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_lead_test_customer
msgid "Check this box if this contact is a customer."
msgstr ""

#. module: base_action_rule
#: model_terms:ir.actions.act_window,help:base_action_rule.base_action_rule_act
msgid "Click to setup a new automated action rule."
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule.lead.test,state:0
msgid "Closed"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_on_change_fields
msgid "Comma-separated list of field names that triggers the onchange."
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid "Conditions"
msgstr "Шарттары"

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_create_date
msgid "Create Date"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_create_uid
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_create_uid
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test_create_uid
msgid "Created by"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_create_date
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test_create_date
msgid "Created on"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule,trg_date_range_type:0
msgid "Days"
msgstr "күн"

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid "Delay After Trigger Date"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_trg_date_range
msgid ""
"Delay after the trigger date.You can put a negative number if you need a "
"delay before thetrigger date, like sending a reminder 15 minutes before a "
"meeting."
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_trg_date_range
msgid "Delay after trigger date"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_trg_date_range_type
msgid "Delay type"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_display_name
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_display_name
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test_display_name
msgid "Display Name"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_filter_domain
msgid "Domain"
msgstr "Домен"

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_server_action_ids
msgid "Examples: email reminders, call object service, etc."
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid "Fields to Change"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_filter_id
msgid "Filter"
msgstr "Сүзгі"

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_sequence
msgid "Gives the sequence order when displaying a list of rules."
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid ""
"Go to your \"Related Document Model\" page and set the filter parameters in "
"the \"Search\" view (Example of filter based on Leads/Opportunities: "
"Creation Date \"is equal to\" 01/01/2012)"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule,trg_date_range_type:0
msgid "Hours"
msgstr "Сағат"

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_id
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_id
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test_id
msgid "ID"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_filter_domain
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_filter_id
msgid ""
"If present, this condition must be satisfied before executing the action "
"rule."
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_filter_pre_domain
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_filter_pre_id
msgid ""
"If present, this condition must be satisfied before the update of the record."
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule.lead.test,state:0
msgid "In Progress"
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid ""
"In this same \"Search\" view, select the menu \"Save Current Filter\", enter "
"the name (Ex: Create the 01/01/2012) and add the option \"Share with all "
"users\""
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_customer
msgid "Is a Customer"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_date_action_last
msgid "Last Action"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule___last_update
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test___last_update
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test___last_update
msgid "Last Modified on"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_last_run
msgid "Last Run"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_write_uid
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test_write_uid
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_write_uid
msgid "Last Updated by"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_write_date
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test_write_date
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_write_date
msgid "Last Updated on"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test_lead_id
msgid "Lead id"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule,trg_date_range_type:0
msgid "Minutes"
msgstr "Минут"

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_model
msgid "Model"
msgstr "Түрі"

#. module: base_action_rule
#: selection:base.action.rule,trg_date_range_type:0
msgid "Months"
msgstr "Айлары"

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test_name
msgid "Name"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule.lead.test,state:0
msgid "New"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_on_change_fields
msgid "On Change Fields Trigger"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule,kind:0
msgid "On Creation"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule,kind:0
msgid "On Creation & Update"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule,kind:0
msgid "On Deletion"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule,kind:0
msgid "On Update"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_partner_id
msgid "Partner"
msgstr ""

#. module: base_action_rule
#: selection:base.action.rule.lead.test,state:0
msgid "Pending"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_model_id
msgid "Related Document Model"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_user_id
msgid "Responsible"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_name
msgid "Rule Name"
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid ""
"Select when the action must be run, and choose records and/or timing "
"conditions."
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_sequence
msgid "Sequence"
msgstr "Тізбек"

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_server_action_ids
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid "Server Actions"
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid "Server actions to run"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_act_user_id
msgid "Set Responsible"
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid "Set selection based on a search filter:"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_state
msgid "Status"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_name
msgid "Subject"
msgstr "Нысаны"

#. module: base_action_rule
#: model:ir.filters,name:base_action_rule.test_filter_done
msgid "Test lead in state 'done'"
msgstr ""

#. module: base_action_rule
#: model:ir.filters,name:base_action_rule.test_filter_draft
msgid "Test lead in state 'draft'"
msgstr ""

#. module: base_action_rule
#: model:ir.filters,name:base_action_rule.test_filter_open
msgid "Test lead in state 'open'"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_trg_date_id
msgid "Trigger Date"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_trg_date_calendar_id
msgid "Use Calendar"
msgstr ""

#. module: base_action_rule
#: model_terms:ir.actions.act_window,help:base_action_rule.base_action_rule_act
msgid ""
"Use automated actions to automatically trigger actions for\n"
"                various screens. Example: a lead created by a specific user "
"may\n"
"                be automatically set to a specific sales team, or an\n"
"                opportunity which still has status pending after 14 days "
"might\n"
"                trigger an automatic reminder email."
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_line_test_user_id
msgid "User id"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possible to use a "
"calendar to compute the date based on working days."
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_trg_date_id
msgid ""
"When should the condition be triggered. If present, will be checked by the "
"scheduler. If empty, will be checked at creation and update."
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_kind
msgid "When to Run"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,help:base_action_rule.field_base_action_rule_active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr ""

#. module: base_action_rule
#: model_terms:ir.ui.view,arch_db:base_action_rule.view_base_action_rule_form
msgid ""
"You may also use filters instead of choosing records. In order to create a "
"new filter:"
msgstr ""

#. module: base_action_rule
#: model:ir.model.fields,field_description:base_action_rule.field_base_action_rule_lead_test_line_ids
msgid "unknown"
msgstr ""
