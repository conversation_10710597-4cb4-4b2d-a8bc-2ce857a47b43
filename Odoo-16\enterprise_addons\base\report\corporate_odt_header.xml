<?xml version="1.0" encoding="UTF-8"?>
<office:document-styles xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0" xmlns:table="urn:oasis:names:tc:opendocument:xmlns:table:1.0" xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0" xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:number="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0" xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0" xmlns:chart="urn:oasis:names:tc:opendocument:xmlns:chart:1.0" xmlns:dr3d="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="urn:oasis:names:tc:opendocument:xmlns:form:1.0" xmlns:script="urn:oasis:names:tc:opendocument:xmlns:script:1.0" xmlns:ooo="http://openoffice.org/2004/office" xmlns:ooow="http://openoffice.org/2004/writer" xmlns:oooc="http://openoffice.org/2004/calc" xmlns:dom="http://www.w3.org/2001/xml-events" office:version="1.1">
	<office:font-face-decls>
		<style:font-face style:name="Helvetica" svg:font-family="Helvetica"/>
		<style:font-face style:name="Times" svg:font-family="Times"/>
		<style:font-face style:name="Helvetica1" svg:font-family="Helvetica" style:font-family-generic="swiss"/>
		<style:font-face style:name="Monospace" svg:font-family="Monospace" style:font-pitch="fixed"/>
		<style:font-face style:name="DejaVu Sans" svg:font-family="&apos;DejaVu Sans&apos;" style:font-family-generic="system" style:font-pitch="variable"/>
	</office:font-face-decls>
	<office:styles>
		<style:default-style style:family="graphic">
			<style:graphic-properties draw:shadow-offset-x="0.3cm" draw:shadow-offset-y="0.3cm" draw:start-line-spacing-horizontal="0.283cm" draw:start-line-spacing-vertical="0.283cm" draw:end-line-spacing-horizontal="0.283cm" draw:end-line-spacing-vertical="0.283cm" style:flow-with-text="true"/>
			<style:paragraph-properties style:text-autospace="ideograph-alpha" style:line-break="strict" style:writing-mode="lr-tb" style:font-independent-line-spacing="false">
				<style:tab-stops/>
			</style:paragraph-properties>
			<style:text-properties style:use-window-font-color="true" fo:font-size="12pt" fo:language="en" fo:country="IN" style:letter-kerning="true" style:font-size-asian="10.5pt" style:language-asian="zxx" style:country-asian="none" style:font-size-complex="12pt" style:language-complex="zxx" style:country-complex="none"/>
		</style:default-style>
		<style:default-style style:family="paragraph">
			<style:paragraph-properties fo:hyphenation-ladder-count="no-limit" style:text-autospace="ideograph-alpha" style:punctuation-wrap="hanging" style:line-break="strict" style:tab-stop-distance="1.251cm" style:writing-mode="page"/>
			<style:text-properties style:use-window-font-color="true" style:font-name="Times" fo:font-size="12pt" fo:language="en" fo:country="IN" style:letter-kerning="true" style:font-name-asian="DejaVu Sans" style:font-size-asian="10.5pt" style:language-asian="zxx" style:country-asian="none" style:font-name-complex="DejaVu Sans" style:font-size-complex="12pt" style:language-complex="zxx" style:country-complex="none" fo:hyphenate="false" fo:hyphenation-remain-char-count="2" fo:hyphenation-push-char-count="2"/>
		</style:default-style>
		<style:default-style style:family="table">
			<style:table-properties table:border-model="collapsing"/>
		</style:default-style>
		<style:default-style style:family="table-row">
			<style:table-row-properties fo:keep-together="always"/>
		</style:default-style>
		<style:style style:name="Standard" style:family="paragraph" style:class="text"/>
			<style:style style:name="Text_20_body" style:display-name="Text body" style:family="paragraph" style:parent-style-name="Standard" style:class="text">
			<style:paragraph-properties fo:margin-top="0cm" fo:margin-bottom="0.212cm"/>
		</style:style>
		<style:style style:name="Heading" style:family="paragraph" style:parent-style-name="Standard" style:next-style-name="Text_20_body" style:class="text">
				<style:paragraph-properties fo:margin-top="0.423cm" fo:margin-bottom="0.212cm" fo:keep-with-next="always"/>
				<style:text-properties style:font-name="Helvetica" fo:font-size="14pt" style:font-name-asian="DejaVu Sans" style:font-size-asian="14pt" style:font-name-complex="DejaVu Sans" style:font-size-complex="14pt"/>
		</style:style>
		<style:style style:name="List" style:family="paragraph" style:parent-style-name="Text_20_body" style:class="list">
			<style:text-properties style:font-name="Times" style:font-size-asian="12pt"/>
		</style:style>
		<style:style style:name="Header" style:family="paragraph" style:parent-style-name="Standard" style:class="extra">
			<style:paragraph-properties text:number-lines="false" text:line-number="0">
				<style:tab-stops>
					<style:tab-stop style:position="8.498cm" style:type="center"/>
					<style:tab-stop style:position="16.999cm" style:type="right"/>
				</style:tab-stops>
			</style:paragraph-properties>
			<style:text-properties fo:color="#0000ff" style:font-size-asian="10.5pt"/>
		</style:style>
		<style:style style:name="Footer" style:family="paragraph" style:parent-style-name="Standard" style:class="extra">
			<style:paragraph-properties text:number-lines="false" text:line-number="0">
				<style:tab-stops>
					<style:tab-stop style:position="8.498cm" style:type="center"/>
					<style:tab-stop style:position="16.999cm" style:type="right"/>
				</style:tab-stops>
			</style:paragraph-properties>
		</style:style>
		<style:style style:name="Table_20_Contents" style:display-name="Table Contents" style:family="paragraph" style:parent-style-name="Standard" style:class="extra">
			<style:paragraph-properties text:number-lines="false" text:line-number="0"/>
		</style:style>
		<style:style style:name="Table_20_Heading" style:display-name="Table Heading" style:family="paragraph" style:parent-style-name="Table_20_Contents" style:class="extra">
			<style:paragraph-properties fo:text-align="center" style:justify-single-word="false" text:number-lines="false" text:line-number="0"/>
			<style:text-properties fo:font-weight="bold" style:fw-asian="bold" style:fw-complex="bold"/>
		</style:style>
		<style:style style:name="Caption" style:family="paragraph" style:parent-style-name="Standard" style:class="extra">
			<style:paragraph-properties fo:margin-top="0.212cm" fo:margin-bottom="0.212cm" text:number-lines="false" text:line-number="0"/>
			<style:text-properties style:font-name="Times" fo:font-size="12pt" fo:font-style="italic" style:font-size-asian="12pt" style:fst-asian="italic" style:font-size-complex="12pt" style:fst-complex="italic"/>
		</style:style>
		<style:style style:name="Index" style:family="paragraph" style:parent-style-name="Standard" style:class="index">
			<style:paragraph-properties text:number-lines="false" text:line-number="0"/>
			<style:text-properties style:font-name="Times" style:font-size-asian="12pt"/>
		</style:style>
		<style:style style:name="Footnote_20_Symbol" style:display-name="Footnote Symbol" style:family="text"/>
		<style:style style:name="Endnote_20_Symbol" style:display-name="Endnote Symbol" style:family="text"/>
			<style:style style:name="Graphics" style:family="graphic">
			<style:graphic-properties text:anchor-type="paragraph" svg:x="0cm" svg:y="0cm" style:wrap="dynamic" style:number-wrapped-paragraphs="no-limit" style:wrap-contour="false" style:vertical-pos="top" style:vertical-rel="paragraph" style:horizontal-pos="center" style:horizontal-rel="paragraph"/>
		</style:style>
		<text:outline-style>
			<text:outline-level-style text:level="1" style:num-format="">
			<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
			<text:outline-level-style text:level="2" style:num-format="">
				<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
			<text:outline-level-style text:level="3" style:num-format="">
				<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
			<text:outline-level-style text:level="4" style:num-format="">
				<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
			<text:outline-level-style text:level="5" style:num-format="">
				<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
			<text:outline-level-style text:level="6" style:num-format="">
				<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
			<text:outline-level-style text:level="7" style:num-format="">
				<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
			<text:outline-level-style text:level="8" style:num-format="">
				<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
			<text:outline-level-style text:level="9" style:num-format="">
				<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
			<text:outline-level-style text:level="10" style:num-format="">
				<style:list-level-properties text:min-label-distance="0.381cm"/>
			</text:outline-level-style>
		</text:outline-style>
		<text:notes-configuration text:note-class="footnote" text:citation-style-name="Footnote_20_Symbol" style:num-format="1" text:start-value="0" text:footnotes-position="page" text:start-numbering-at="page"/>
		<text:notes-configuration text:note-class="endnote" text:citation-style-name="Endnote_20_Symbol" text:master-page-name="Endnote" style:num-format="1" text:start-value="0"/>
		<text:linenumbering-configuration text:number-lines="false" text:offset="0.499cm" style:num-format="1" text:number-position="left" text:increment="5"/>
	</office:styles>
	<office:automatic-styles>
		<style:style style:name="Table2" style:family="table">
			<style:table-properties style:width="16.999cm" table:align="margins"/>
		</style:style>
		<style:style style:name="Table2.A" style:family="table-column">
			<style:table-column-properties style:column-width="4.235cm" style:rel-column-width="16329*"/>
		</style:style>
		<style:style style:name="Table2.B" style:family="table-column">
			<style:table-column-properties style:column-width="12.764cm" style:rel-column-width="49206*"/>
		</style:style>
		<style:style style:name="Table2.A1" style:family="table-cell">
			<style:table-cell-properties style:vertical-align="bottom" fo:padding="0.097cm" fo:border-start="none" fo:border-end="none" fo:border-top="none" fo:border-bottom="0.018cm solid #000000"/>
		</style:style>
		<style:style style:name="Table3" style:family="table">
			<style:table-properties style:width="7.936cm" table:align="left"/>
		</style:style>
		<style:style style:name="Table3.A" style:family="table-column">
			<style:table-column-properties style:column-width="1.933cm"/>
		</style:style>
		<style:style style:name="Table3.B" style:family="table-column">
			<style:table-column-properties style:column-width="6.003cm"/>
		</style:style>
		<style:style style:name="Table3.A1" style:family="table-cell">
			<style:table-cell-properties style:vertical-align="bottom" fo:padding="0.097cm" fo:border="none"/>
		</style:style>
		<style:style style:name="Table3.A2" style:family="table-cell">
			<style:table-cell-properties style:vertical-align="bottom" fo:padding="0.097cm" fo:border-start="none" fo:border-end="none" fo:border-top="none" fo:border-bottom="0.018cm solid #000000"/>
		</style:style>
		<style:style style:name="Table1" style:family="table">
			<style:table-properties style:width="16.999cm" table:align="margins"/>
		</style:style>
		<style:style style:name="Table1.A" style:family="table-column">
			<style:table-column-properties style:column-width="16.999cm" style:rel-column-width="65535*"/>
		</style:style>
		<style:style style:name="Table1.A1" style:family="table-cell">
			<style:table-cell-properties fo:background-color="transparent" fo:padding="0.097cm" fo:border-start="none" fo:border-end="none" fo:border-top="0.018cm solid #000000" fo:border-bottom="none">
			<style:background-image/>
			</style:table-cell-properties>
		</style:style>
		<style:style style:name="P1" style:family="paragraph" style:parent-style-name="Header">
			<style:text-properties fo:color="#0000ff" style:font-name="Helvetica1" fo:font-size="30pt" style:font-name-asian="Monospace" style:font-size-asian="30pt" style:font-name-complex="Monospace" style:font-size-complex="30pt"/>
			</style:style>
		<style:style style:name="P2" style:family="paragraph" style:parent-style-name="Table_20_Contents">
			<style:paragraph-properties fo:text-align="end" style:justify-single-word="false"/>
			<style:text-properties fo:color="#0000ff" style:font-name="Helvetica1" fo:font-size="10pt" style:font-name-asian="Monospace" style:font-size-asian="10pt" style:font-name-complex="Monospace" style:font-size-complex="10pt"/>
		</style:style>
		<style:style style:name="P3" style:family="paragraph" style:parent-style-name="Header">
			<style:text-properties style:font-name="Monospace" fo:font-size="10pt" style:font-name-asian="Monospace" style:font-size-asian="10pt" style:font-name-complex="Monospace" style:font-size-complex="10pt"/>
		</style:style>
		<style:style style:name="P4" style:family="paragraph" style:parent-style-name="Table_20_Contents">
			<style:text-properties fo:color="#0000ff" style:font-name="Helvetica1" fo:font-size="10pt" style:font-size-asian="8.75pt" style:font-size-complex="10pt"/>
		</style:style>
		<style:style style:name="P5" style:family="paragraph" style:parent-style-name="Table_20_Contents">
			<style:paragraph-properties fo:text-align="end" style:justify-single-word="false"/>
			<style:text-properties fo:color="#0000ff" style:font-name="Monospace" fo:font-size="10pt" style:font-name-asian="Monospace" style:font-size-asian="10pt" style:font-name-complex="Monospace" style:font-size-complex="10pt"/>
		</style:style>
		<style:style style:name="P6" style:family="paragraph" style:parent-style-name="Header">
			<style:text-properties style:font-name="Helvetica1" fo:font-size="10pt" style:font-size-asian="8.75pt" style:font-size-complex="10pt"/>
		</style:style>
		<style:style style:name="P7" style:family="paragraph" style:parent-style-name="Footer">
			<style:paragraph-properties fo:text-align="center" style:justify-single-word="false"/>
			<style:text-properties fo:color="#0000ff" style:font-name="Helvetica1" fo:font-size="10pt" style:font-name-asian="Monospace" style:font-size-asian="10pt" style:font-name-complex="Monospace" style:font-size-complex="10pt"/>
		</style:style>
		<style:page-layout style:name="pm1">
			<style:page-layout-properties fo:page-width="20.999cm" fo:page-height="29.699cm" style:num-format="1" style:print-orientation="portrait" fo:margin-top="2cm" fo:margin-bottom="2cm" fo:margin-left="2cm" fo:margin-right="2cm" style:writing-mode="lr-tb" style:footnote-max-height="0cm">
				<style:footnote-sep style:width="0.018cm" style:distance-before-sep="0.101cm" style:distance-after-sep="0.101cm" style:adjustment="left" style:rel-width="25%" style:color="#000000"/>
			</style:page-layout-properties>
			<style:header-style>
				<style:header-footer-properties fo:min-height="0cm" fo:margin-left="0cm" fo:margin-right="0cm" fo:margin-bottom="0.499cm"/>
			</style:header-style>
			<style:footer-style>
				<style:header-footer-properties fo:min-height="0cm" fo:margin-left="0cm" fo:margin-right="0cm" fo:margin-top="0.499cm"/>
			</style:footer-style>
		</style:page-layout>
		<style:page-layout style:name="pm2">
			<style:page-layout-properties fo:page-width="20.999cm" fo:page-height="29.699cm" style:num-format="1" style:print-orientation="portrait" fo:margin-top="2cm" fo:margin-bottom="2cm" fo:margin-left="2cm" fo:margin-right="2cm" style:writing-mode="lr-tb" style:footnote-max-height="0cm">
				<style:footnote-sep style:adjustment="left" style:rel-width="25%" style:color="#000000"/>
			</style:page-layout-properties>
			<style:header-style/>
			<style:footer-style/>
		</style:page-layout>
		</office:automatic-styles>
		<office:master-styles>
			<style:master-page style:name="Standard" style:page-layout-name="pm1">
				<style:header>
				<table:table table:name="Table2" table:style-name="Table2">
					<table:table-column table:style-name="Table2.A"/>
					<table:table-column table:style-name="Table2.B"/>
					<table:table-row>
						<table:table-cell table:style-name="Table2.A1" office:value-type="string">
							<text:p text:style-name="P1">Tiny sprl</text:p>
						</table:table-cell>
						<table:table-cell table:style-name="Table2.A1" office:value-type="string">
							<text:p text:style-name="P2"/>
						</table:table-cell>
					</table:table-row>
				</table:table>
			<text:p text:style-name="P3"/>
			<text:p text:style-name="P3">- </text:p>
			<table:table table:name="Table3" table:style-name="Table3">
				<table:table-column table:style-name="Table3.A"/>
				<table:table-column table:style-name="Table3.B"/>
				<table:table-row>
					<table:table-cell table:style-name="Table3.A1" office:value-type="string">
						<text:p text:style-name="P4">Phone :</text:p>
					</table:table-cell>
					<table:table-cell table:style-name="Table3.A1" office:value-type="string">
						<text:p text:style-name="P5"/>
					</table:table-cell>
				</table:table-row>
				<table:table-row>
					<table:table-cell table:style-name="Table3.A2" office:value-type="string">
						<text:p text:style-name="P4">Mail :</text:p>
					</table:table-cell>
					<table:table-cell table:style-name="Table3.A2" office:value-type="string">
						<text:p text:style-name="P5"/>
					</table:table-cell>
				</table:table-row>
			</table:table>
			<text:p text:style-name="P6"/>
		</style:header>
		<style:footer>
			<table:table table:name="Table1" table:style-name="Table1">
				<table:table-column table:style-name="Table1.A"/>
				<table:table-row>
					<table:table-cell table:style-name="Table1.A1" office:value-type="string">
						<text:p text:style-name="P7"/>
						<text:p text:style-name="P7"/>
						<text:p text:style-name="P7">Contact : Administrator</text:p>
					</table:table-cell>
				</table:table-row>
			</table:table>
		</style:footer>
	</style:master-page>
	<style:master-page style:name="Endnote" style:page-layout-name="pm2"/>
	</office:master-styles>
</office:document-styles>