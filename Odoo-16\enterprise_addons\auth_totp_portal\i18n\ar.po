# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_portal
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid " Copy"
msgstr "نسخ "

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "(Disable two-factor authentication)"
msgstr "(تعطيل المصادقة ثنائية العوامل) "

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        Two-factor authentication not enabled"
msgstr ""
"<i class=\"fa fa-warning\"/>\n"
"                        المصادقة ثنائية العوامل غير مفعلة "

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"التوثيق \" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<span class=\"text-success\">\n"
"                        <i class=\"fa fa-check-circle\"/>\n"
"                        Two-factor authentication enabled\n"
"                    </span>"
msgstr ""
"<span class=\"text-success\">\n"
"                        <i class=\"fa fa-check-circle\"/>\n"
"                        تم تفعيل المصادقة ثنائية العوامل\n"
"                    </span>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Added On</strong>"
msgstr "<strong>تمت إضافته في</strong>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Trusted Device</strong>"
msgstr "<strong>جهاز موثوق</strong>"

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Copied !"
msgstr "تم النسخ!"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Enable two-factor authentication"
msgstr "تفعيل المصادقة ثنائية العوامل "

#. module: auth_totp_portal
#. odoo-javascript
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Operation failed for unknown reason."
msgstr "فشلت العملية لسبب غير معروف "

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Revoke All"
msgstr "إبطال الكل "

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Two-factor authentication"
msgstr "المصادقة ثنائية العوامل "

#. module: auth_totp_portal
#: model:ir.model,name:auth_totp_portal.model_res_users
msgid "User"
msgstr "المستخدم"
