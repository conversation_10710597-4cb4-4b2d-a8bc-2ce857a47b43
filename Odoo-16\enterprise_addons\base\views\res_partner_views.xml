<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Partner Titles -->
        <record id="view_partner_title_tree" model="ir.ui.view">
            <field name="name">res.partner.title.tree</field>
            <field name="model">res.partner.title</field>
            <field name="arch" type="xml">
                <tree string="Partner Titles" editable="bottom">
                    <field name="name"/>
                    <field name="shortcut"/>
                </tree>
            </field>
        </record>
        <record id="view_partner_title_form" model="ir.ui.view">
            <field name="name">res.partner.title.form</field>
            <field name="model">res.partner.title</field>
            <field name="arch" type="xml">
                <form string="Partner Titles">
                    <sheet>
                        <group col="4">
                            <field name="name"/>
                            <field name="shortcut"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="action_partner_title_contact" model="ir.actions.act_window">
            <field name="name">Contact Titles</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.partner.title</field>
            <field name="domain">[]</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a Title
                </p><p>
                    Manage Contact Titles as well as their abbreviations (e.g. "Mr.", "Mrs.", etc).
                </p>
            </field>
        </record>

        <!-- PARTNER TREE VIEW + MUTI_EDIT: VISIBLE FIELDS WITH ONCHANGE ON BASE/PARTNER
             VIEW WON'T BE EDITABLE ON "MULTI_EDIT" MODE:
                - parent_id
                - country_id
                - state_id
                - company_type
                - company_id
        -->
        <!-- Partner -->
        <record id="view_partner_tree" model="ir.ui.view">
            <field name="name">res.partner.tree</field>
            <field name="model">res.partner</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Contacts" sample="1" multi_edit="1">
                    <field name="display_name" string="Name" invisible="1"/>
                    <field name="translated_display_name" string="Name"/>
                    <field name="function" invisible="1"/>
                    <field name="phone" class="o_force_ltr" optional="show"/>
                    <field name="email" optional="show"/>
                    <field name="user_id" optional="show" widget="many2one_avatar_user" domain="[('share', '=', False)]"/>
                    <field name="city" optional="show"/>
                    <field name="state_id" optional="hide" readonly="1"/>
                    <field name="country_id" optional="show" readonly="1"/>
                    <field name="vat" optional="hide" readonly="1"/>
                    <field name="category_id" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
                    <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                    <field name="is_company" invisible="1"/>
                    <field name="parent_id" invisible="1" readonly="1"/>
                    <field name="active" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_partner_simple_form" model="ir.ui.view">
            <field name="name">res.partner.simplified.form</field>
            <field name="model">res.partner</field>
            <field name="arch" type="xml">
                <form string="Contact">
                    <field name="is_company" invisible="1"/>
                    <field name="type" invisible="1"/>
                    <field name="avatar_128" invisible="1"/>
                    <field name="user_id" invisible="1"/>
                    <field name="image_1920" widget='image' class="oe_avatar" options='{"preview_image": "avatar_128"}'/>
                    <div class="oe_title">
                        <field name="company_type" options="{'horizontal': true}" widget="radio" groups="base.group_no_one"/>
                        <h1>
                            <field id="company" name="name" default_focus="1" placeholder="e.g. Lumber Inc" attrs="{'required' : [('type', '=', 'contact'),('is_company','=', True)], 'invisible': [('is_company','=', False)]}"/>
                            <field id="individual" name="name" default_focus="1" placeholder="e.g. Brandom Freeman" attrs="{'required' : [('type', '=', 'contact'),('is_company','=', False)], 'invisible': [('is_company','=', True)]}"/>
                        </h1>
                        <field name="parent_id"
                            widget="res_partner_many2one"
                            placeholder="Company Name..."
                            domain="[('is_company', '=', True)]" context="{'default_is_company': True, 'show_vat': True, 'default_user_id': user_id}"
                            attrs="{'invisible': [('is_company','=', True)]}"/>
                    </div>
                    <group>
                        <field name="function" placeholder="e.g. Sales Director" attrs="{'invisible': [('is_company','=', True)]}"/>
                        <field name="user_ids" invisible="1"/>
                        <field name="email" widget="email" context="{'gravatar_image': True}" required="context.get('force_email', False)" attrs="{'required': [('user_ids','!=', [])]}"/>
                        <field name="phone" widget="phone" options="{'enable_sms': false}"/>
                        <field name="mobile" widget="phone" options="{'enable_sms': false}"/>
                    </group>
                </form>
            </field>
        </record>

        <!-- Open partner address -->
        <record id="view_partner_address_form" model="ir.ui.view">
            <field name="name">res.partner.form.address</field>
            <field name="model">res.partner</field>
            <field name="priority" eval="20"/>
            <field name="arch" type="xml">
                <form string="Partner">
                    <field name="avatar_128" invisible="1"/>
                    <field name="image_1920" widget='image' class="oe_avatar" options='{"preview_image": "avatar_128"}' readonly="1"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <field name="parent_id" invisible="1"/>
                    <group>
                        <group>
                            <label for="type" attrs="{'invisible': [('parent_id','=', False)]}" groups="base.group_no_one"/>
                            <div attrs="{'invisible': [('parent_id','=', False)]}" name="div_type" groups="base.group_no_one">
                                <field name="type" class="oe_inline"/>
                            </div>
                            <label for="street" string="Address"/>
                            <div class="o_address_format">
                                <field name="street" placeholder="Street..." class="o_address_street"/>
                                <field name="street2" placeholder="Street 2..." class="o_address_street"/>
                                <field name="city" placeholder="City" class="o_address_city"/>
                                <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}" context="{'default_country_id': country_id}"/>
                                <field name="zip" placeholder="ZIP" class="o_address_zip"/>
                                <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'/>
                            </div>
                            <field name="website" string="Website" widget="url" placeholder="e.g. https://www.odoo.com"/>
                        </group>
                        <group>
                        </group>
                    </group>
                </form>
            </field>
        </record>

        <record id="view_partner_form" model="ir.ui.view">
            <field name="name">res.partner.form</field>
            <field name="model">res.partner</field>
            <field name="priority" eval="1"/>
            <field name="arch" type="xml">
                <form string="Partners">
                <div class="alert alert-warning oe_edit_only" role="alert" attrs="{'invisible': [('same_vat_partner_id', '=', False)]}">
                  A partner with the same <span><span class="o_vat_label">Tax ID</span></span> already exists (<field name="same_vat_partner_id"/>), are you sure to create a new one?
                </div>
                <div class="alert alert-warning oe_edit_only" role="alert" attrs="{'invisible': [('same_company_registry_partner_id', '=', False)]}">
                  A partner with the same <span><span class="o_vat_label">Company Registry</span></span> already exists (<field name="same_company_registry_partner_id"/>), are you sure to create a new one?
                </div>
                <sheet>
                    <div class="oe_button_box" name="button_box"/>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <field name="avatar_128" invisible="1"/>
                    <field name="image_1920" widget='image' class="oe_avatar" options='{"preview_image": "avatar_128"}'/>
                    <div class="oe_title mb24">
                        <field name="is_company" invisible="1"/>
                        <field name="commercial_partner_id" invisible="1"/>
                        <field name="active" invisible="1"/>
                        <field name="company_id" invisible="1"/>
                        <field name="country_code" invisible="1"/>
                        <field name="company_type" widget="radio" options="{'horizontal': true}"/>
                        <h1>
                            <field id="company" class="text-break" name="name" default_focus="1" placeholder="e.g. Lumber Inc" attrs="{'required' : [('type', '=', 'contact')], 'invisible': [('is_company','=', False)]}"/>
                            <field id="individual" class="text-break" name="name" default_focus="1" placeholder="e.g. Brandom Freeman" attrs="{'required' : [('type', '=', 'contact')], 'invisible': [('is_company','=', True)]}"/>
                        </h1>
                        <div class="o_row">
                            <field name="parent_id"
                                widget="res_partner_many2one"
                                placeholder="Company Name..."
                                domain="[('is_company', '=', True)]" context="{'default_is_company': True, 'show_vat': True, 'default_user_id': user_id}"
                                attrs="{'invisible': ['|', '&amp;', ('is_company','=', True),('parent_id', '=', False),('company_name', '!=', False),('company_name', '!=', '')]}"/>
                                <field name="company_name" attrs="{'invisible': ['|', '|', ('company_name', '=', False), ('company_name', '=', ''), ('is_company', '=', True)]}"/>
                                <button name="create_company" icon="fa-plus-square" string="Create company"
                                    type="object" class="oe_edit_only btn-link"
                                    attrs="{'invisible': ['|', '|', ('is_company','=', True), ('company_name', '=', ''), ('company_name', '=', False)]}"/>
                        </div>
                    </div>

                    <group>
                        <group>
                            <span class="o_form_label o_td_label" name="address_name">
                                <field name="type" attrs="{'invisible': [('is_company','=', True)], 'required': [('is_company','!=', True)], 'readonly': [('user_ids', '!=', [])]}" class="fw-bold"/>
                                <b attrs="{'invisible': [('is_company', '=', False)]}">Address</b>
                            </span>
                            <div class="o_address_format">
                                <field name="street" placeholder="Street..." class="o_address_street"
                                    attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <field name="street2" placeholder="Street 2..." class="o_address_street"
                                    attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <field name="city" placeholder="City" class="o_address_city"
                                    attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}"
                                    attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                                <field name="zip" placeholder="ZIP" class="o_address_zip"
                                    attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <div name="partner_address_country" class="d-flex justify-content-between">
                                    <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'
                                        attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                </div>
                            </div>
                            <field name="vat" placeholder="e.g. BE0477472701" attrs="{'readonly': [('parent_id','!=',False)]}"/>
                        </group>
                        <group>
                            <field name="function" placeholder="e.g. Sales Director"
                                attrs="{'invisible': [('is_company','=', True)]}"/>
                            <field name="phone" widget="phone"/>
                            <field name="mobile" widget="phone"/>
                            <field name="user_ids" invisible="1"/>
                            <field name="email" widget="email" context="{'gravatar_image': True}" attrs="{'required': [('user_ids','!=', [])]}"/>
                            <field name="website" string="Website" widget="url" placeholder="e.g. https://www.odoo.com"/>
                            <field name="title" options='{"no_open": True}' placeholder="e.g. Mister"
                                attrs="{'invisible': [('is_company', '=', True)]}"/>
                            <field name="active_lang_count" invisible="1"/>
                            <field name="lang" attrs="{'invisible': [('active_lang_count', '&lt;=', 1)]}"/>
                            <field name="category_id" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}"
                                   placeholder='e.g. "B2B", "VIP", "Consulting", ...'/>
                        </group>
                    </group>

                    <notebook colspan="4">
                        <page string="Contacts &amp; Addresses" name="contact_addresses" autofocus="autofocus">
                            <field name="child_ids" mode="kanban" context="{'default_parent_id': active_id, 'default_street': street, 'default_street2': street2, 'default_city': city, 'default_state_id': state_id, 'default_zip': zip, 'default_country_id': country_id, 'default_lang': lang, 'default_user_id': user_id, 'default_type': 'other'}">
                                <kanban>
                                    <field name="id"/>
                                    <field name="color"/>
                                    <field name="name"/>
                                    <field name="title"/>
                                    <field name="type"/>
                                    <field name="email"/>
                                    <field name="parent_id"/>
                                    <field name="is_company"/>
                                    <field name="function"/>
                                    <field name="phone"/>
                                    <field name="street"/>
                                    <field name="street2"/>
                                    <field name="zip"/>
                                    <field name="city"/>
                                    <field name="country_id"/>
                                    <field name="mobile"/>
                                    <field name="state_id"/>
                                    <field name="image_128"/>
                                    <field name="avatar_128"/>
                                    <field name="lang"/>
                                    <!-- fields in form x2many view to diminish requests -->
                                    <field name="comment"/>
                                    <field name="display_name"/>
                                    <templates>
                                        <t t-name="kanban-box">
                                            <t t-set="color" t-value="kanban_color(record.color.raw_value)"/>
                                            <div t-att-class="color + (record.title.raw_value == 1 ? ' oe_kanban_color_alert' : '') + ' oe_kanban_global_click'">
                                                <div class="o_kanban_image">
                                                    <img alt="Contact image" t-att-src="kanban_image('res.partner', 'avatar_128', record.id.raw_value)"/>
                                                </div>
                                                <div class="oe_kanban_details">
                                                    <field name="name"/>
                                                    <div t-if="record.function.raw_value"><field name="function"/></div>
                                                    <div t-if="record.email.raw_value"><field name="email" widget="email"/></div>
                                                    <div t-if="record.type.raw_value != 'contact'">
                                                        <div>
                                                            <field name="zip"/><t t-if="record.city"> </t>
                                                            <field name="city"/>
                                                        </div>
                                                        <field t-if="record.state_id.raw_value" name="state_id"/><t t-if="record.country_id"> </t>
                                                        <field name="country_id"/>
                                                    </div>
                                                    <div t-if="record.phone.raw_value">Phone: <t t-esc="record.phone.value"/></div>
                                                    <div t-if="record.mobile.raw_value">Mobile: <t t-esc="record.mobile.value"/></div>
                                                </div>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                                <form string="Contact / Address">
                                    <sheet>
                                        <field name="type" required="1" widget="radio" options="{'horizontal': true}"/>
                                        <field name="parent_id" invisible="1"/>
                                        <div class="text-muted oe_edit_only">
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'contact')]}">
                                                <span>Use this to organize the contact details of employees of a given company (e.g. CEO, CFO, ...).</span>
                                            </p>
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'invoice')]}">
                                                <span>Preferred address for all invoices. Selected by default when you invoice an order that belongs to this company.</span>
                                            </p>
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'delivery')]}">
                                                <span>Preferred address for all deliveries. Selected by default when you deliver an order that belongs to this company.</span>
                                            </p>
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'private')]}">
                                                <span>Private addresses are only visible by authorized users and contain sensitive data (employee home addresses, ...).</span>
                                            </p>
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'other')]}">
                                                <span>Other address for the company (e.g. subsidiary, ...)</span>
                                            </p>
                                        </div>
                                        <hr/>
                                        <group>
                                            <group>
                                                <field name="name" string="Contact Name" attrs="{'required' : [('type', '=', 'contact')]}"/>
                                                <field name="title" options="{'no_open': True}" placeholder="e.g. Mr."
                                                    attrs="{'invisible': [('type','!=', 'contact')]}"/>
                                                <field name="function" placeholder="e.g. Sales Director"
                                                    attrs="{'invisible': [('type','!=', 'contact')]}"/>
                                                <label for="street" string="Address" attrs="{'invisible': [('type','=', 'contact')]}"/>
                                                <div attrs="{'invisible': [('type','=', 'contact')]}">
                                                    <div class="o_address_format" name="div_address">
                                                        <field name="street" placeholder="Street..." class="o_address_street"/>
                                                        <field name="street2" placeholder="Street 2..." class="o_address_street"/>
                                                        <field name="city" placeholder="City" class="o_address_city"/>
                                                        <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                                                        <field name="zip" placeholder="ZIP" class="o_address_zip"/>
                                                        <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'/>
                                                    </div>
                                                </div>
                                            </group>
                                            <group>
                                                <field name="email" widget="email"/>
                                                <field name="phone" widget="phone"/>
                                                <field name="mobile" widget="phone"/>
                                                <field name="company_id" invisible="1"/>
                                            </group>
                                        </group>
                                        <group>
                                            <field name="comment" placeholder="Internal notes..." nolabel="1" colspan="2"/>
                                        </group>
                                        <field name="lang" invisible="True"/>
                                        <field name="user_id" invisible="True"/>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                        <page name='sales_purchases' string="Sales &amp; Purchase">
                            <group name="container_row_2">
                                <group string="Sales" name="sale" priority="1">
                                    <field name="user_id" widget="many2one_avatar_user" domain="[('share', '=', False)]"/>
                                </group>
                                <group string="Purchase" name="purchase" priority="2">
                                </group>
                                <group name="misc" string="Misc">
                                    <field name="company_registry" attrs="{'invisible': [('parent_id','!=',False)]}"/>
                                    <field name="ref" string="Reference"/>
                                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" attrs="{'readonly': [('parent_id', '!=', False)]}" force_save="1"/>
                                    <field name="industry_id" attrs="{'invisible': [('is_company', '=', False)]}" options="{'no_create': True}"/>
                                </group>
                            </group>
                        </page>
                        <page name='internal_notes' string="Internal Notes">
                            <field name="comment" placeholder="Internal notes..."/>
                        </page>
                    </notebook>
                </sheet>
                </form>
            </field>
        </record>

      <!-- Special restricted view for private address, with limited "named" info that
           can be traced back to the employee -->
        <record id="res_partner_view_form_private" model="ir.ui.view">
            <field name="name">res.partner.view.form.private</field>
            <field name="model">res.partner</field>
            <field name="priority" eval="300"/>
            <field name="arch" type="xml">
                <form string="Private Address Form">
                    <sheet>
                        <field name="type" invisible="1"/>
                        <field name="parent_id" invisible="1"/>
                        <label for="name" class="oe_edit_only"/>
                        <field name="name" required="0"/>
                        <group>
                            <group>
                                <label for="street" string="Address"/>
                                <div>
                                    <div class="o_address_format" name="div_address">
                                        <field name="street" placeholder="Street..." class="o_address_street"/>
                                        <field name="street2" placeholder="Street 2..." class="o_address_street"/>
                                        <field name="city" placeholder="City" class="o_address_city"/>
                                        <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                                        <field name="zip" placeholder="ZIP" class="o_address_zip"/>
                                        <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'/>
                                    </div>
                                </div>
                            </group>
                            <group>
                                <field name="phone" widget="phone" options="{'enable_sms': false}"/>
                                <field name="mobile" widget="phone" options="{'enable_sms': false}"/>
                                <field name="email"/>
                                <field name="lang"/>
                            </group>
                        </group>
                        <group string="Bank Accounts">
                            <field name="bank_ids">
                                <tree editable="bottom">
                                    <field name="bank_id"/>
                                    <field name="acc_number"/>
                                    <field name="acc_holder_name" invisible="1"/>
                                </tree>
                            </field>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

       <record id="view_res_partner_filter" model="ir.ui.view">
            <field name="name">res.partner.select</field>
            <field name="model">res.partner</field>
            <field name="arch" type="xml">
                <search string="Search Partner">
                    <field name="name"
                       filter_domain="['|', '|', '|', '|', ('display_name', 'ilike', self), ('ref', 'ilike', self), ('email', 'ilike', self), ('vat', 'ilike', self), ('company_registry', 'ilike', self)]"/>
                    <field name="parent_id" domain="[('is_company', '=', True)]" operator="child_of"/>
                    <field name="email" filter_domain="[('email', 'ilike', self)]"/>
                    <field name="phone" filter_domain="['|', ('phone', 'ilike', self), ('mobile', 'ilike', self)]"/>
                    <field name="category_id" string="Tag" operator="child_of"/>
                    <field name="user_id"/>
                    <separator/>
                    <filter string="Individuals" name="type_person" domain="[('is_company', '=', False)]"/>
                    <filter string="Companies" name="type_company" domain="[('is_company', '=', True)]"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <group expand="0" name="group_by" string="Group By">
                        <filter name="salesperson" string="Salesperson" domain="[]" context="{'group_by' : 'user_id'}" />
                        <filter name="group_company" string="Company" context="{'group_by': 'parent_id'}"/>
                        <filter name="group_country" string="Country" context="{'group_by': 'country_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Partner Kanban View -->
        <record model="ir.ui.view" id="res_partner_kanban_view">
            <field name="name">res.partner.kanban</field>
            <field name="model">res.partner</field>
            <field name="arch" type="xml">
                <kanban class="o_res_partner_kanban" sample="1">
                    <field name="id"/>
                    <field name="color"/>
                    <field name="display_name"/>
                    <field name="title"/>
                    <field name="email"/>
                    <field name="parent_id"/>
                    <field name="is_company"/>
                    <field name="function"/>
                    <field name="phone"/>
                    <field name="street"/>
                    <field name="street2"/>
                    <field name="zip"/>
                    <field name="city"/>
                    <field name="country_id"/>
                    <field name="mobile"/>
                    <field name="state_id"/>
                    <field name="category_id"/>
                    <field name="avatar_128"/>
                    <field name="type"/>
                    <field name="active"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click o_kanban_record_has_image_fill o_res_partner_kanban">
                                <t t-if="!record.is_company.raw_value">
                                    <t t-set="background_image" t-value="kanban_image('res.partner', 'avatar_128', record.id.raw_value)"/>
                                    <div class="o_kanban_image_fill_left d-none d-md-block" t-attf-style="background-image:url('#{background_image}')">
                                        <img class="o_kanban_image_inner_pic" t-if="record.parent_id.raw_value" t-att-alt="record.parent_id.value" t-att-src="kanban_image('res.partner', 'avatar_128', record.parent_id.raw_value)"/>
                                    </div>
                                    <div class="o_kanban_image d-md-none" t-attf-style="background-image: url(#{background_image})">
                                        <img class="o_kanban_image_inner_pic" t-if="record.parent_id.raw_value" t-att-alt="record.parent_id.value" t-att-src="kanban_image('res.partner', 'avatar_128', record.parent_id.raw_value)"/>
                                    </div>
                                </t>
                                <t t-else="">
                                    <div class="o_kanban_image_fill_left o_kanban_image_full" t-attf-style="background-image: url(#{kanban_image('res.partner', 'avatar_128', record.id.raw_value)})" role="img"/>
                                </t>
                                <div class="ribbon ribbon-top-right" attrs="{'invisible': [('active', '=', True)]}">
                                    <span class="bg-danger">Archived</span>
                                </div>
                                <div class="oe_kanban_details d-flex flex-column justify-content-between">
                                    <div>
                                        <strong class="o_kanban_record_title oe_partner_heading"><field name="display_name" invisible="1"/></strong>
                                        <strong class="o_kanban_record_title oe_partner_heading"><field name="translated_display_name"/></strong>
                                        <div class="o_kanban_tags_section oe_kanban_partner_categories"/>
                                        <ul>
                                            <li t-if="record.parent_id.raw_value and !record.function.raw_value"><field name="parent_id"/></li>
                                            <li t-elif="!record.parent_id.raw_value and record.function.raw_value"><field name="function"/></li>
                                            <li t-elif="record.parent_id.raw_value and record.function.raw_value"><field name="function"/> at <field name="parent_id"/></li>
                                            <li t-if="record.city.raw_value or record.country_id.raw_value">
                                                <t t-if="record.city.raw_value"><field name="city"/><t t-if="record.country_id.raw_value">, </t></t>
                                                <t t-if="record.country_id.raw_value"><field name="country_id"/></t>
                                            </li>
                                            <li t-if="record.email.raw_value" class="o_text_overflow"><field name="email"/></li>
                                        </ul>
                                    </div>
                                    <div class="o_kanban_record_bottom">
                                        <div class="oe_kanban_bottom_left"/>
                                        <div class="oe_kanban_bottom_right"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="action_partner_form" model="ir.actions.act_window">
            <field name="name">Customers</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="context">{'res_partner_search_mode': 'customer'}</field>
            <field name="search_view_id" ref="view_res_partner_filter"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a Contact in your address book
              </p><p>
                Odoo helps you track all activities related to your contacts.
              </p>
            </field>
        </record>
        <record id="action_partner_form_view1" model="ir.actions.act_window.view">
            <field eval="0" name="sequence"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="res_partner_kanban_view"/>
            <field name="act_window_id" ref="action_partner_form"/>
        </record>
        <record id="action_partner_form_view2" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_partner_form"/>
            <field name="act_window_id" ref="action_partner_form"/>
        </record>
        <record id="action_partner_tree_view1" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="view_partner_tree"/>
            <field name="act_window_id" ref="action_partner_form"/>
        </record>

        <record id="action_partner_customer_form" model="ir.actions.act_window">
            <field name="name">Customers</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="domain">[]</field>
            <field name="context">{'res_partner_search_mode': 'customer', 'default_is_company': True}</field>
            <field name="filter" eval="True"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new customer in your address book
              </p><p>
                Odoo helps you easily track all activities related to a customer.
              </p>
            </field>
        </record>
        <record id="action_partner_customer_form_view1" model="ir.actions.act_window.view">
            <field eval="1" name="sequence"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="res_partner_kanban_view"/>
            <field name="act_window_id" ref="action_partner_customer_form"/>
        </record>
        <record id="action_partner_customer_form_view2" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="view_partner_tree"/>
            <field name="act_window_id" ref="action_partner_customer_form"/>
        </record>
        <record id="action_partner_customer_form_view3" model="ir.actions.act_window.view">
            <field eval="3" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_partner_form"/>
            <field name="act_window_id" ref="action_partner_customer_form"/>
        </record>

        <record id="action_partner_supplier_form" model="ir.actions.act_window">
            <field name="name">Vendors</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.partner</field>
            <field name="domain">[]</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="context">{'res_partner_search_mode': 'supplier', 'default_is_company': True}</field>
            <field name="filter" eval="True"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new vendor in your address book
              </p><p>
                Odoo helps you easily track all activities related to a vendor.
              </p>
            </field>
        </record>
        <record id="action_partner_vendor_form_view1" model="ir.actions.act_window.view">
            <field eval="1" name="sequence"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="res_partner_kanban_view"/>
            <field name="act_window_id" ref="action_partner_supplier_form"/>
        </record>
        <record id="action_partner_vendor_form_view2" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="view_partner_tree"/>
            <field name="act_window_id" ref="action_partner_supplier_form"/>
        </record>
        <record id="action_partner_vendor_form_view3" model="ir.actions.act_window.view">
            <field eval="3" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_partner_form"/>
            <field name="act_window_id" ref="action_partner_supplier_form"/>
        </record>

        <!-- Categories -->
        <record id="view_partner_category_form" model="ir.ui.view">
            <field name="name">Contact Tags</field>
            <field name="model">res.partner.category</field>
            <field name="arch" type="xml">
                <form string="Contact Tag">
                    <sheet>
                        <group col="4">
                            <field name="name" placeholder='e.g. "Consulting Services"'/>
                            <field name="color" widget="color_picker"/>
                            <field name="parent_id"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_partner_category_list" model="ir.ui.view">
            <field name="name">Contact Tags</field>
            <field name="model">res.partner.category</field>
            <field eval="6" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Contact Tags">
                    <field name="display_name"/>
                    <field name="color" widget="color_picker"/>
                </tree>
            </field>
        </record>

        <record id="res_partner_category_view_search" model="ir.ui.view">
            <field name="name">res.partner.category.view.search</field>
            <field name="model">res.partner.category</field>
            <field name="arch" type="xml">
                <search string="Search Partner Category">
                    <field name="name"/>
                    <field name="display_name"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                </search>
            </field>
        </record>

        <record id="action_partner_category_form" model="ir.actions.act_window">
            <field name="name">Contact Tags</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.partner.category</field>
            <field name="search_view_id" ref="res_partner_category_view_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a Contact Tag
              </p><p>
                Assign tags to your contacts to organize, filter and track them.
              </p>
            </field>
        </record>

        <!-- Industry -->
        <record id="res_partner_industry_view_form" model="ir.ui.view">
            <field name="name">Industry</field>
            <field name="model">res.partner.industry</field>
            <field name="arch" type="xml">
                <form string="Industry">
                    <sheet>
                        <group col="4">
                            <field name="name"/>
                            <field name="full_name"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="res_partner_industry_view_tree" model="ir.ui.view">
            <field name="name">Industry</field>
            <field name="model">res.partner.industry</field>
            <field eval="6" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Industry" editable="bottom">
                    <field name="name"/>
                    <field name="full_name"/>
                    <field name="active" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="res_partner_industry_view_search" model="ir.ui.view">
            <field name="name">res.partner.industry.view.search</field>
            <field name="model">res.partner.industry</field>
            <field name="arch" type="xml">
                <search string="Search Partner Industry">
                    <field name="name"/>
                    <field name="full_name"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                </search>
            </field>
        </record>

        <record id="res_partner_industry_action" model="ir.actions.act_window">
            <field name="name">Industries</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.partner.industry</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="res_partner_industry_view_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create an Industry
              </p><p>
                Specify industries to classify your contacts and draw up reports.
              </p>
            </field>
        </record>
    </data>
</odoo>
