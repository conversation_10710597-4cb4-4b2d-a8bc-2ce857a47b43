<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="wizard_lang_export" model="ir.ui.view">
            <field name="name">Export Translations</field>
            <field name="model">base.language.export</field>
            <field name="arch" type="xml">
                <form string="Export Translations">
                    <field invisible="1" name="state"/>
                    <field name="name" invisible="1"/>
                    <group states="choose" string="Export Settings">
                        <field name="lang"/>
                        <field name="format"/>
                        <field name="modules" widget="many2many_tags" options="{'no_create': True}"/>
                    </group>
                    <div states="get">
                        <h2>Export Complete</h2>
                        <p>Here is the exported translation file: <field name="data" readonly="1" filename="name"/></p>
                        <p>This file was generated using the universal <strong>Unicode/UTF-8</strong> file encoding, please be sure to view and edit
                           using the same encoding.</p> 
                        <p>The next step depends on the file format:
                            <ul>
                            <li>CSV format: you may edit it directly with your favorite spreadsheet software,
                                the rightmost column (value) contains the translations</li>
                            <li>PO(T) format: you should edit it with a PO editor such as
                                <a href="http://www.poedit.net/" target="_blank">POEdit</a>, or your preferred text editor</li>
                            <li>TGZ format: bundles multiple PO(T) files as a single archive</li>
                            </ul>
                        </p>
                        <p>For more details about translating Odoo in your language, please refer to the
                           <a href="https://github.com/odoo/odoo/wiki/Translations" target="_blank">documentation</a>.</p>
                    </div>
                    <footer states="choose">
                        <button name="act_getfile" string="Export" type="object" class="btn-primary" data-hotkey="q"/>
                        <button special="cancel" data-hotkey="z" string="Cancel" type="object" class="btn-secondary"/>
                    </footer>
                    <footer states="get">
                        <button special="cancel" data-hotkey="z" string="Close" type="object" class="btn-primary"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_wizard_lang_export" model="ir.actions.act_window">
            <field name="name">Export Translation</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">base.language.export</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
        <menuitem action="action_wizard_lang_export" id="menu_wizard_lang_export" parent="menu_translation_export"/>
    </data>
</odoo>
