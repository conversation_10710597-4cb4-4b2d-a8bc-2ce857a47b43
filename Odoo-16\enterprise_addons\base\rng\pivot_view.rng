<?xml version="1.0" encoding="UTF-8"?>
<rng:grammar xmlns:rng="http://relaxng.org/ns/structure/1.0"
             xmlns:a="http://relaxng.org/ns/annotation/1.0"
             datatypeLibrary="http://www.w3.org/2001/XMLSchema-datatypes">
    <!-- Handling of element overloading when inheriting from a base
         template
    -->
    <rng:include href="common.rng"/>
    <rng:define name="pivot">
        <rng:element name="pivot">
            <rng:optional><rng:attribute name="sample"/></rng:optional>
            <rng:optional><rng:attribute name="string"/></rng:optional>
            <rng:optional><rng:attribute name="stacked"/></rng:optional>
            <rng:optional><rng:attribute name="display_quantity"/></rng:optional>
            <rng:optional><rng:attribute name="disable_linking"/></rng:optional>
            <rng:optional><rng:attribute name="js_class"/></rng:optional>
            <rng:optional><rng:attribute name="default_order"/></rng:optional>
            <rng:zeroOrMore>
                <rng:ref name="field"/>
            </rng:zeroOrMore>
        </rng:element>
    </rng:define>
    <rng:start>
        <rng:choice>
            <rng:ref name="pivot" />
        </rng:choice>
    </rng:start>
</rng:grammar>
