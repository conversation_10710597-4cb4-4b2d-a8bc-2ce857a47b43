<?xml version="1.0" encoding="UTF-8"?>
<office:document-styles xmlns:office="http://openoffice.org/2000/office" xmlns:style="http://openoffice.org/2000/style" xmlns:text="http://openoffice.org/2000/text" xmlns:table="http://openoffice.org/2000/table" xmlns:draw="http://openoffice.org/2000/drawing" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="http://openoffice.org/2000/meta" xmlns:number="http://openoffice.org/2000/datastyle" xmlns:svg="http://www.w3.org/2000/svg" xmlns:chart="http://openoffice.org/2000/chart" xmlns:dr3d="http://openoffice.org/2000/dr3d" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="http://openoffice.org/2000/form" xmlns:script="http://openoffice.org/2000/script" xmlns:ooo="http://openoffice.org/2004/office" xmlns:ooow="http://openoffice.org/2004/writer" xmlns:oooc="http://openoffice.org/2004/calc" xmlns:dom="http://www.w3.org/2001/xml-events" office:version="1.0">
    <office:font-decls>
        <style:font-decl style:name="Helvetica" fo:font-family="Helvetica"/>
        <style:font-decl style:name="Times" fo:font-family="Times"/>
        <style:font-decl style:name="Helvetica1" fo:font-family="Helvetica" style:font-family-generic="swiss"/>
        <style:font-decl style:name="Monospace" fo:font-family="Monospace" style:font-pitch="fixed"/>
        <style:font-decl style:name="DejaVu Sans" fo:font-family="&apos;DejaVu Sans&apos;" style:font-family-generic="system" style:font-pitch="variable"/>
    </office:font-decls>
    <office:styles>
        <style:default-style style:family="graphics">
            <style:properties draw:shadow-offset-x="0.3cm" draw:shadow-offset-y="0.3cm" draw:start-line-spacing-horizontal="0.283cm" draw:start-line-spacing-vertical="0.283cm" draw:end-line-spacing-horizontal="0.283cm" draw:end-line-spacing-vertical="0.283cm" style:text-autospace="ideograph-alpha" style:line-break="strict" style:writing-mode="lr-tb" style:font-independent-line-spacing="false" style:use-window-font-color="true" fo:font-size="12pt" fo:language="en" fo:country="IN" style:letter-kerning="true" style:font-size-asian="10.5pt" style:language-asian="zxx" style:country-asian="none" style:font-size-complex="12pt" style:language-complex="zxx" style:country-complex="none">
                <style:tab-stops/>
            </style:properties>
        </style:default-style>
        <style:default-style style:family="paragraph">
            <style:properties fo:hyphenation-ladder-count="no-limit" style:text-autospace="ideograph-alpha" style:punctuation-wrap="hanging" style:line-break="strict" style:tab-stop-distance="1.251cm" style:writing-mode="page" style:use-window-font-color="true" style:font-name="Times" fo:font-size="12pt" fo:language="en" fo:country="IN" style:letter-kerning="true" style:font-name-asian="DejaVu Sans" style:font-size-asian="10.5pt" style:language-asian="zxx" style:country-asian="none" style:font-name-complex="DejaVu Sans" style:font-size-complex="12pt" style:language-complex="zxx" style:country-complex="none" fo:hyphenate="false" fo:hyphenation-remain-char-count="2" fo:hyphenation-push-char-count="2"/>
        </style:default-style>
        <style:default-style style:family="table">
            <style:properties table:border-model="collapsing"/>
        </style:default-style>
        <style:default-style style:family="table-row">
            <style:properties fo:keep-together="always"/>
        </style:default-style>
        <style:style style:name="Standard" style:family="paragraph" style:class="text"/>
        <style:style style:name="Text body" style:family="paragraph" style:parent-style-name="Standard" style:class="text">
            <style:properties fo:margin-top="0cm" fo:margin-bottom="0.212cm"/>
        </style:style>
        <style:style style:name="Heading" style:family="paragraph" style:parent-style-name="Standard" style:next-style-name="Text body" style:class="text">
            <style:properties fo:margin-top="0.423cm" fo:margin-bottom="0.212cm" fo:keep-with-next="true" style:font-name="Helvetica" fo:font-size="14pt" style:font-name-asian="DejaVu Sans" style:font-size-asian="14pt" style:font-name-complex="DejaVu Sans" style:font-size-complex="14pt"/>
        </style:style>
        <style:style style:name="List" style:family="paragraph" style:parent-style-name="Text body" style:class="list">
            <style:properties style:font-name="Times" style:font-size-asian="12pt"/>
        </style:style>
        <style:style style:name="Header" style:family="paragraph" style:parent-style-name="Standard" style:class="extra">
            <style:properties text:number-lines="false" text:line-number="0" fo:color="#0000ff" style:font-size-asian="10.5pt">
                <style:tab-stops>
                    <style:tab-stop style:position="8.498cm" style:type="center"/>
                    <style:tab-stop style:position="16.999cm" style:type="right"/>
                </style:tab-stops>
            </style:properties>
        </style:style>
        <style:style style:name="Footer" style:family="paragraph" style:parent-style-name="Standard" style:class="extra">
            <style:properties text:number-lines="false" text:line-number="0">
                <style:tab-stops>
                    <style:tab-stop style:position="8.498cm" style:type="center"/>
                    <style:tab-stop style:position="16.999cm" style:type="right"/>
                </style:tab-stops>
            </style:properties>
        </style:style>
        <style:style style:name="Table Contents" style:family="paragraph" style:parent-style-name="Standard" style:class="extra">
            <style:properties text:number-lines="false" text:line-number="0"/>
        </style:style>
        <style:style style:name="Table Heading" style:family="paragraph" style:parent-style-name="Table Contents" style:class="extra">
            <style:properties fo:text-align="center" style:justify-single-word="false" text:number-lines="false" text:line-number="0" fo:font-weight="bold" style:fw-asian="bold" style:fw-complex="bold"/>
        </style:style>
        <style:style style:name="Caption" style:family="paragraph" style:parent-style-name="Standard" style:class="extra">
            <style:properties fo:margin-top="0.212cm" fo:margin-bottom="0.212cm" text:number-lines="false" text:line-number="0" style:font-name="Times" fo:font-size="12pt" fo:font-style="italic" style:font-size-asian="12pt" style:fst-asian="italic" style:font-size-complex="12pt" style:fst-complex="italic"/>
        </style:style>
        <style:style style:name="Index" style:family="paragraph" style:parent-style-name="Standard" style:class="index">
            <style:properties text:number-lines="false" text:line-number="0" style:font-name="Times" style:font-size-asian="12pt"/>
        </style:style>
        <style:style style:name="Footnote Symbol" style:family="text"/>
        <style:style style:name="Endnote Symbol" style:family="text"/>
        <style:style style:name="Graphics" style:family="graphics">
            <style:properties text:anchor-type="paragraph" svg:x="0cm" svg:y="0cm" style:wrap="dynamic" style:number-wrapped-paragraphs="no-limit" style:wrap-contour="false" style:vertical-pos="top" style:vertical-rel="paragraph" style:horizontal-pos="center" style:horizontal-rel="paragraph"/>
        </style:style>
        <text:outline-style>
            <text:outline-level-style text:level="1" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
            <text:outline-level-style text:level="2" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
            <text:outline-level-style text:level="3" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
            <text:outline-level-style text:level="4" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
            <text:outline-level-style text:level="5" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
            <text:outline-level-style text:level="6" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
            <text:outline-level-style text:level="7" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
            <text:outline-level-style text:level="8" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
            <text:outline-level-style text:level="9" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
            <text:outline-level-style text:level="10" style:num-format="">
                <style:properties text:min-label-distance="0.381cm"/>
            </text:outline-level-style>
        </text:outline-style>
        <text:footnotes-configuration text:citation-style-name="Footnote Symbol" style:num-format="1" text:start-value="0" text:footnotes-position="page" text:start-numbering-at="page"/>
        <text:endnotes-configuration text:citation-style-name="Endnote Symbol" text:master-page-name="Endnote" style:num-format="1" text:start-value="0"/>
        <text:linenumbering-configuration text:number-lines="false" text:offset="0.499cm" style:num-format="1" text:number-position="left" text:increment="5"/>
    </office:styles>
    <office:automatic-styles>
        <style:style style:name="Table2" style:family="table">
            <style:properties style:width="16.999cm" table:align="margins"/>
        </style:style>
        <style:style style:name="Table2.A" style:family="table-column">
            <style:properties style:column-width="4.235cm" style:rel-column-width="16329*"/>
        </style:style>
        <style:style style:name="Table2.B" style:family="table-column">
            <style:properties style:column-width="12.764cm" style:rel-column-width="49206*"/>
        </style:style>
        <style:style style:name="Table2.A1" style:family="table-cell">
            <style:properties fo:vertical-align="bottom" fo:padding="0.097cm" fo:border-start="none" fo:border-end="none" fo:border-top="none" fo:border-bottom="0.018cm solid #000000"/>
        </style:style>
        <style:style style:name="Table3" style:family="table">
            <style:properties style:width="7.936cm" table:align="left"/>
        </style:style>
        <style:style style:name="Table3.A" style:family="table-column">
            <style:properties style:column-width="1.933cm"/>
        </style:style>
        <style:style style:name="Table3.B" style:family="table-column">
            <style:properties style:column-width="6.003cm"/>
        </style:style>
        <style:style style:name="Table3.A1" style:family="table-cell">
            <style:properties fo:vertical-align="bottom" fo:padding="0.097cm" fo:border="none"/>
        </style:style>
        <style:style style:name="Table3.A2" style:family="table-cell">
            <style:properties fo:vertical-align="bottom" fo:padding="0.097cm" fo:border-start="none" fo:border-end="none" fo:border-top="none" fo:border-bottom="0.018cm solid #000000"/>
        </style:style>
        <style:style style:name="Table1" style:family="table">
            <style:properties style:width="16.999cm" table:align="margins"/>
        </style:style>
        <style:style style:name="Table1.A" style:family="table-column">
            <style:properties style:column-width="16.999cm" style:rel-column-width="65535*"/>
        </style:style>
        <style:style style:name="Table1.A1" style:family="table-cell">
            <style:properties fo:background-color="transparent" fo:padding="0.097cm" fo:border-start="none" fo:border-end="none" fo:border-top="0.018cm solid #000000" fo:border-bottom="none">
                <style:background-image/>
            </style:properties>
        </style:style>
        <style:style style:name="P1" style:family="paragraph" style:parent-style-name="Header">
            <style:properties fo:color="#0000ff" style:font-name="Helvetica1" fo:font-size="30pt" style:font-name-asian="Monospace" style:font-size-asian="30pt" style:font-name-complex="Monospace" style:font-size-complex="30pt"/>
        </style:style>
        <style:style style:name="P2" style:family="paragraph" style:parent-style-name="Table Contents">
            <style:properties fo:text-align="end" style:justify-single-word="false" fo:color="#0000ff" style:font-name="Helvetica1" fo:font-size="10pt" style:font-name-asian="Monospace" style:font-size-asian="10pt" style:font-name-complex="Monospace" style:font-size-complex="10pt"/>
        </style:style>
        <style:style style:name="P3" style:family="paragraph" style:parent-style-name="Header">
            <style:properties style:font-name="Monospace" fo:font-size="10pt" style:font-name-asian="Monospace" style:font-size-asian="10pt" style:font-name-complex="Monospace" style:font-size-complex="10pt"/>
        </style:style>
        <style:style style:name="P4" style:family="paragraph" style:parent-style-name="Table Contents">
            <style:properties fo:color="#0000ff" style:font-name="Helvetica1" fo:font-size="10pt" style:font-size-asian="8.75pt" style:font-size-complex="10pt"/>
        </style:style>
        <style:style style:name="P5" style:family="paragraph" style:parent-style-name="Table Contents">
            <style:properties fo:text-align="end" style:justify-single-word="false" fo:color="#0000ff" style:font-name="Monospace" fo:font-size="10pt" style:font-name-asian="Monospace" style:font-size-asian="10pt" style:font-name-complex="Monospace" style:font-size-complex="10pt"/>
        </style:style>
        <style:style style:name="P6" style:family="paragraph" style:parent-style-name="Header">
            <style:properties style:font-name="Helvetica1" fo:font-size="10pt" style:font-size-asian="8.75pt" style:font-size-complex="10pt"/>
        </style:style>
        <style:style style:name="P7" style:family="paragraph" style:parent-style-name="Footer">
            <style:properties fo:text-align="center" style:justify-single-word="false" fo:color="#0000ff" style:font-name="Helvetica1" fo:font-size="10pt" style:font-name-asian="Monospace" style:font-size-asian="10pt" style:font-name-complex="Monospace" style:font-size-complex="10pt"/>
        </style:style>
        <style:style style:name="P8" style:family="paragraph" style:parent-style-name="Footer">
            <style:properties fo:text-align="center" style:justify-single-word="false" style:font-name="Helvetica1" fo:font-size="10pt" style:font-size-asian="10pt" style:font-size-complex="10pt"/>
        </style:style>
        <style:style style:name="T1" style:family="text">
            <style:properties fo:color="#0000ff"/>
        </style:style>
        <style:style style:name="T2" style:family="text">
            <style:properties fo:color="#0000ff" fo:font-size="10pt" style:font-name-asian="Monospace" style:font-size-asian="10pt" style:font-name-complex="Monospace" style:font-size-complex="10pt"/>
        </style:style>
        <style:page-master style:name="pm1">
            <style:properties fo:page-width="20.999cm" fo:page-height="29.699cm" style:num-format="1" style:print-orientation="portrait" fo:margin-top="2cm" fo:margin-bottom="2cm" fo:margin-left="2cm" fo:margin-right="2cm" style:writing-mode="lr-tb" style:footnote-max-height="0cm">
                <style:footnote-sep style:width="0.018cm" style:distance-before-sep="0.101cm" style:distance-after-sep="0.101cm" style:adjustment="left" style:rel-width="25%" style:color="#000000"/>
            </style:properties>
            <style:header-style>
                <style:properties fo:min-height="0cm" fo:margin-left="0cm" fo:margin-right="0cm" fo:margin-bottom="0.499cm"/>
            </style:header-style>
            <style:footer-style>
                <style:properties fo:min-height="0cm" fo:margin-left="0cm" fo:margin-right="0cm" fo:margin-top="0.499cm"/>
            </style:footer-style>
        </style:page-master>
        <style:page-master style:name="pm2">
            <style:properties fo:page-width="20.999cm" fo:page-height="29.699cm" style:num-format="1" style:print-orientation="portrait" fo:margin-top="2cm" fo:margin-bottom="2cm" fo:margin-left="2cm" fo:margin-right="2cm" style:writing-mode="lr-tb" style:footnote-max-height="0cm">
                <style:footnote-sep style:adjustment="left" style:rel-width="25%" style:color="#000000"/>
            </style:properties>
            <style:header-style/>
            <style:footer-style/>
        </style:page-master>
    </office:automatic-styles>
    <office:master-styles>
        <style:master-page style:name="Standard" style:page-master-name="pm1">
            <style:header>
                <table:table table:name="Table2" table:style-name="Table2">
                    <table:table-column table:style-name="Table2.A"/>
                    <table:table-column table:style-name="Table2.B"/>
                    <table:table-row>
                        <table:table-cell table:style-name="Table2.A1" table:value-type="string">
                            <text:p text:style-name="P1">[[ company.partner_id.name ]]</text:p>
                        </table:table-cell>
                        <table:table-cell table:style-name="Table2.A1" table:value-type="string">
                            <text:p text:style-name="P2">[[ company.report_header ]]</text:p>
                        </table:table-cell>
                    </table:table-row>
                </table:table>
                <text:p text:style-name="P3">[[ company.partner_id.street ]]</text:p>
                <text:p text:style-name="P3">[[ company.partner_id.zip ]] [[ company.partner_id.city ]] - [[ company.partner_id.country_id and company.partner_id.country_id.name ]]</text:p>
                <table:table table:name="Table3" table:style-name="Table3">
                    <table:table-column table:style-name="Table3.A"/>
                    <table:table-column table:style-name="Table3.B"/>
                    <table:table-row>
                        <table:table-cell table:style-name="Table3.A1" table:value-type="string">
                            <text:p text:style-name="P4">Phone :</text:p>
                        </table:table-cell>
                        <table:table-cell table:style-name="Table3.A1" table:value-type="string">
                            <text:p text:style-name="P5">[[ company.partner_id.phone ]]</text:p>
                        </table:table-cell>
                    </table:table-row>
                    <table:table-row>
                        <table:table-cell table:style-name="Table3.A2" table:value-type="string">
                            <text:p text:style-name="P4">Mail :</text:p>
                        </table:table-cell>
                        <table:table-cell table:style-name="Table3.A2" table:value-type="string">
                            <text:p text:style-name="P5">[[ company.partner_id.email ]]</text:p>
                        </table:table-cell>
                    </table:table-row>
                </table:table>
                <text:p text:style-name="P6"/>
            </style:header>
            <style:footer>
                <table:table table:name="Table1" table:style-name="Table1">
                    <table:table-column table:style-name="Table1.A"/>
                    <table:table-row>
                        <table:table-cell table:style-name="Table1.A1" table:value-type="string">
                            <text:p text:style-name="P7">[[ company.report_footer ]]</text:p>
                            <text:p text:style-name="P7">Contact : [[ user.name ]]</text:p>
                        </table:table-cell>
                    </table:table-row>
                </table:table>
            </style:footer>
        </style:master-page>
        <style:master-page style:name="Endnote" style:page-master-name="pm2"/>
    </office:master-styles>
</office:document-styles>
