<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_currency_rate_search" model="ir.ui.view">
            <field name="name">res.currency.rate.search</field>
            <field name="model">res.currency.rate</field>
            <field name="arch" type="xml">
                <search string="Currency Rates">
                    <field name="name" string="Date"/>
                </search>
            </field>
        </record>

        <record id="view_currency_rate_tree" model="ir.ui.view">
            <field name="name">res.currency.rate.tree</field>
            <field name="model">res.currency.rate</field>
            <field name="arch" type="xml">
                <tree string="Currency Rates" editable="bottom">
                    <field name="name"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="company_rate" digits="[12,12]"/>
                    <field name="inverse_company_rate" digits="[12,12]"/>
                    <field name="rate" digits="[12,12]" optional="hide"/>
                    <field name="write_date" optional="hide"/>
                </tree>
            </field>
        </record>

        <record id="view_currency_rate_form" model="ir.ui.view">
            <field name="name">res.currency.rate.form</field>
            <field name="model">res.currency.rate</field>
            <field name="arch" type="xml">
                <form string="Currency Rate">
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="rate" digits="[12,12]" groups="base.group_no_one"/>
                                <field name="company_rate" digits="[12,12]"/>
                                <field name="inverse_company_rate" digits="[12,12]"/>
                            </group>
                            <group>
                                <field name="currency_id"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="act_view_currency_rates" model="ir.actions.act_window">
            <field name="name">Show Currency Rates</field>
            <field name="res_model">res.currency.rate</field>
            <field name='view_mode'>tree,form</field>
            <field name='view_id' ref='view_currency_rate_tree'/>
            <field name="domain">[('currency_id','=', active_id)]</field>
            <field name="context">{'default_currency_id': active_id}</field>
            <!-- <field name="target">fullscreen</field> -->
            <field name="binding_model_id" ref="model_res_currency"/>
            <field name="binding_view_types">form</field>
        </record>

        <record id="view_currency_search" model="ir.ui.view">
            <field name="name">res.currency.search</field>
            <field name="model">res.currency</field>
            <field name="arch" type="xml">
                <search string="Currencies">
                    <field name="name" string="Currency" filter_domain="('|','|','|','|',
                                                                        ('name', 'ilike', self),
                                                                        ('full_name', 'ilike', self),
                                                                        ('symbol', 'ilike', self),
                                                                        ('currency_unit_label', 'ilike', self),
                                                                        ('currency_subunit_label', 'ilike', self),
                                                                        )"/>
                    <filter name="active" string="Active" domain="[('active','=',True)]" help="Show active currencies"/>
                    <filter name="inactive" string="Inactive" domain="[('active','=',False)]" help="Show inactive currencies"/>
                </search>
            </field>
        </record>

        <record id="view_currency_tree" model="ir.ui.view">
            <field name="name">res.currency.tree</field>
            <field name="model">res.currency</field>
            <field name="arch" type="xml">
                <tree string="Currencies" decoration-muted="(not active)">
                    <field name="name"/>
                    <field name="symbol"/>
                    <field name="full_name" string="Name" optional="show"/>
                    <field name="date" string="Last Update"/>
                    <field name="rate" digits="[12,6]"/>
                    <field name="inverse_rate" digits="[12,6]" optional="hide"/>
                    <field name="active" widget="boolean_toggle"/>
                </tree>
            </field>
        </record>

        <record id="view_currency_kanban" model="ir.ui.view">
            <field name="name">res.currency.kanban</field>
            <field name="model">res.currency</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="symbol"/>
                    <field name="full_name"/>
                    <field name="active"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <div class="col-2">
                                        <h3><t t-esc="record.name.value"/></h3>
                                    </div>
                                    <div class="col-5">
                                        <span class="badge rounded-pill"><t t-esc="record.symbol.value"/></span>
                                    </div>
                                    <div class="col-5 text-end">
                                        <t t-if="! record.active.raw_value"><span class="badge rounded-pill bg-light border">inactive</span></t>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div><field name="rate_string"/></div>
                                        <t t-if="record.date.raw_value"><div>Last update: <field name="date"/></div></t>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_currency_form" model="ir.ui.view">
            <field name="name">res.currency.form</field>
            <field name="model">res.currency</field>
            <field name="arch" type="xml">
                <form string="Currency">
                    <field name="is_current_company_currency" invisible="1"/>
                    <div class="oe_edit_only alert alert-info text-center" role="alert" groups="base.group_no_one">
                        You cannot reduce the number of decimal places of a currency already used on an accounting entry.
                    </div>

                    <div class="alert alert-info text-center" role="alert" attrs="{'invisible': [('is_current_company_currency','=',False)]}">
                        This is your company's currency.
                    </div>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="full_name" string="Name"/>
                                <field name="active" widget="boolean_toggle"/>
                            </group>
                            <group>
                                <field name="currency_unit_label"/>
                                <field name="currency_subunit_label"/>
                            </group>
                        </group>

                        <group groups="base.group_no_one">
                            <group string="Price Accuracy">
                                <field name="rounding"/>
                                <field name="decimal_places"/>
                            </group>

                            <group string="Display">
                                <field name="symbol"/>
                                <field name="position"/>
                            </group>
                        </group>
                        <notebook class="o_currency_rate_list" attrs="{'invisible': [('is_current_company_currency','=',True)]}">
                            <page string="Rates" name="rates">
                                <field name="rate_ids" widget="one2many">
                                    <tree string="Rates"  editable="top" limit="25">
                                        <field name="name"/>
                                        <field name="company_id" groups="base.group_multi_company"/>
                                        <field name="company_rate" digits="[12,12]"/>
                                        <field name="inverse_company_rate" digits="[12,12]"/>
                                        <field name="rate" digits="[12,12]" optional="hide"/>
                                        <field name="write_date" optional="hide"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="action_currency_form" model="ir.actions.act_window">
            <field name="name">Currencies</field>
            <field name="res_model">res.currency</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_currency_search"/>
            <field name="context">{'active_test': False}</field>
        </record>

    </data>
</odoo>
