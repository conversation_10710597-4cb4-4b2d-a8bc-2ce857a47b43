<?xml version="1.0" encoding="UTF-8" ?>
<odoo>

    <record id="address_street_extended_form" model="ir.ui.view">
        <field name="name">partner.form.address.extended</field>
        <field name="model">res.partner</field>
        <field name="priority" eval="900"/>
        <field name="arch" type="xml">
            <form>
                <div class="o_address_format">
                    <field name="country_enforce_cities" invisible="1"/>
                    <field name="parent_id" invisible="1"/>
                    <field name="type" invisible="1"/>
                    <field name="street" placeholder="Street..." class="o_address_street oe_read_only"
                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    <div class="oe_edit_only o_row">
                        <field name="street_name" placeholder="Street" style="flex: 3 1 auto"
                               attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <span> </span>
                        <field name="street_number" placeholder="House #" style="flex: 1 1 auto"
                               attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <span> - </span>
                        <field name="street_number2" placeholder="Door #" style="flex: 1 1 auto"
                               attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    </div>
                    <field name="street2" placeholder="Street 2..." class="o_address_street"
                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    <field name="city_id"
                           placeholder="City"
                           class="o_address_city"
                           domain="[('country_id', '=', country_id)]"
                           attrs="{'invisible': [('country_enforce_cities', '=', False)], 'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"
                           context="{'default_country_id': country_id, 'default_state_id': state_id, 'default_zipcode': zip}"/>
                    <field name="city"
                           placeholder="City"
                           class="o_address_city"
                           attrs="{'invisible': [('country_enforce_cities', '=', True), '|', ('city_id', '!=', False), ('city', 'in', ('',False))],
                                   'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    <field name="state_id"
                           class="o_address_state"
                           placeholder="State"
                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"
                           options="{'no_open': True, 'no_quick_create': True}"
                           context="{'default_country_id': country_id}"/>
                    <field name="zip" placeholder="ZIP" class="o_address_zip"
                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    <field name="country_id"
                           placeholder="Country"
                           class="o_address_country"
                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"
                           options="{'no_open': True, 'no_create': True}"/>
                </div>
            </form>
        </field>
    </record>

    <record id="address_street_extended_city_form" model="ir.ui.view">
        <field name="name">partner.form.address.extended.city_id</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <field name="city" position="attributes">
                <attribute name="attrs">
                    {'invisible': [('country_enforce_cities', '=', True), '|', ('city_id', '!=', False), ('city', 'in', ('',False))],
                     'readonly': [('type', '=', 'contact'), ('parent_id', '!=', False)]}
                </attribute>
            </field>
            <field name="city" position="before">
                <field name="country_enforce_cities" invisible="1"/>
                <field name="city_id"
                       placeholder="City"
                       class="o_address_city"
                       domain="[('country_id', '=', country_id)]"
                       attrs="{'invisible': [('country_enforce_cities', '=', False)], 'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"
                       context="{'default_country_id': country_id, 'default_state_id': state_id, 'default_zipcode': zip}"/>
            </field>

            <xpath expr="//field[@name='child_ids']//form//field[@name='city']" position="attributes">
                <attribute name="attrs">
                    {'invisible': [('country_enforce_cities', '=', True), '|', ('city_id', '!=', False), ('city', 'in', ('',False))],
                     'readonly': [('type', '=', 'contact'), ('parent_id', '!=', False)]}
                </attribute>
            </xpath>
            <xpath expr="//field[@name='child_ids']//form//field[@name='city']" position="before">
                <field name="country_enforce_cities" invisible="1"/>
                <field name="city_id"
                       placeholder="City"
                       class="o_address_city"
                       domain="[('country_id', '=', country_id)]"
                       attrs="{'invisible': [('country_enforce_cities', '=', False)], 'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"
                       context="{'default_country_id': country_id, 'default_state_id': state_id, 'default_zipcode': zip}"/>
            </xpath>
        </field>
    </record>

</odoo>
