# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_address_extended
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.2\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-11 14:34+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: base_address_extended
#: model:ir.model.fields,help:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_users__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"Marqueu aquesta casella per assegurar-vos que totes les adreces creades en "
"aquest país tinguin una 'ciutat' escollida a la llista de ciutats del país."

#. module: base_address_extended
#: model:ir.actions.act_window,name:base_address_extended.action_res_city_tree
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_country_city_extended_form
msgid "Cities"
msgstr "Ciutats"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_city
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_city_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_tree
msgid "City"
msgstr "Ciutat"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__city_id
msgid "City ID"
msgstr "ID de la ciutat"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_country
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__country_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Country"
msgstr "País"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_date
msgid "Created on"
msgstr "Creat el"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__display_name
msgid "Display Name"
msgstr "Nom de visualització"

#. module: base_address_extended
#: model_terms:ir.actions.act_window,help:base_address_extended.action_res_city_tree
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"                your partner records. Note that an option can be set on each country separately\n"
"                to enforce any address of it to have a city in this list."
msgstr ""
"Mostra i gestiona la llista de totes les ciutats a que es poden assignar als\n"
"partners. Tingueu en compte la opció es pot configurar a cada país per separat\n"
"per assegurar que qualsevol adreça té una ciutat d'aquesta llista."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number2
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number2
msgid "Door"
msgstr "Porta"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Door #"
msgstr "Porta #"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__country_enforce_cities
msgid "Enforce Cities"
msgstr "Fer complir les ciutats"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number
msgid "House"
msgstr "Casa"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "House #"
msgstr "Casa #"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__id
msgid "ID"
msgstr "ID"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__name
msgid "Name"
msgstr "Nom"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
msgid "Search City"
msgstr "Cerca a la ciutat"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__state_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "State"
msgstr "Província"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street"
msgstr "Carrer"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street 2..."
msgstr "2a adreça"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_name
msgid "Street Name"
msgstr "Nom del carrer"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street..."
msgstr "Carrer..."

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "ZIP"
msgstr "C.P."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__zipcode
msgid "Zip"
msgstr "Codi Postal"
