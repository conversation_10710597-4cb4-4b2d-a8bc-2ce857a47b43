# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# <PERSON><PERSON><PERSON> <piotr.w.<PERSON>@gmail.com>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <s<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON> <judyta.<PERSON><PERSON><PERSON>@openglobe.pl>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <karol<PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <sz<PERSON><PERSON><PERSON><EMAIL>>, 2022
# Martin Trigaux, 2022
# <AUTHOR> <EMAIL>, 2023
# Marta Wacławek, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Marta Wacławek, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"\"\n"
"                (ID:"
msgstr ""
"\"\n"
"(ID:"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__help
msgid "Action Description"
msgstr "Opis akcji"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Action Name"
msgstr "Nazwa działania"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__state
msgid "Action To Do"
msgstr "Akcja do wykonania"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__type
msgid "Action Type"
msgstr "Typ akcji"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "Aktywne"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_type_id
msgid "Activity"
msgstr "Czynność"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_type
msgid "Activity User Type"
msgstr "Czynność Typ Użytkownika"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__partner_ids
msgid "Add Followers"
msgstr "Dodaj obserwatorów"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "Zastosuj do"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "Zarchiwizowane"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
msgid "Automated Action"
msgstr "Automatyczne akcje"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
msgid "Automated Actions"
msgstr "Automatyczne akcje"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation"
msgstr "Automatyzacja"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
#: model:ir.cron,cron_name:base_automation.ir_cron_data_base_automation_check
msgid "Base Action Rule: check and execute"
msgstr "Zasada działania podstawowego: sprawdź i wykonaj"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "Based on Form Modification"
msgstr "bazujące na formularzu modyfikacji"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on Timed Condition"
msgstr "Bazujące na warunku czasu"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "Domena przed aktualizacją"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_model_id
msgid "Binding Model"
msgstr "Model wiążący"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_type
msgid "Binding Type"
msgstr "Typ wiążący"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_view_types
msgid "Binding View Types"
msgstr "Typy widoku Rozliczeń"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__child_ids
msgid "Child Actions"
msgstr "Akcje podrzędne"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__child_ids
msgid ""
"Child server actions that will be executed. Note that the last return "
"returned action value will be used as global return value."
msgstr ""
"Działania serwera potomnego, które zostaną wykonane. Zwróć uwagę, że "
"ostatnia zwracana wartość akcji zostanie użyta jako globalna wartość "
"zwracana."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__mail_post_method
msgid ""
"Choose method for email sending:\n"
"EMail: send directly emails\n"
"Post as Message: post on document and notify followers\n"
"Post as Note: log a note on document"
msgstr ""
"Wybierz metodę wysyłania emaili:\n"
"Email: wysyłaj emaile bezpośrednie\n"
"Postuj jako Wiadomość: zapostuj wiadomość w dokumencie i powiadom followersów\n"
"Postuj jako Notatka: zapisz notatkę w dokumencie"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
msgid "Days"
msgstr "Dni"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date.\n"
"        You can put a negative number if you need a delay before the\n"
"        trigger date, like sending a reminder 15 minutes before a meeting."
msgstr ""
"Opóźnienie po dacie wyzwolenia.\n"
"Możesz wprowadzić liczbę ujemną, jeśli potrzebujesz opóźnienia przed datą wyzwalacza.\n"
"na przykład wysłanie przypomnienia 15 minut przed spotkaniem."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "Opóźnienie po dacie wyzwolenia"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "Typ opóźnienia"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Disable Action"
msgstr "Wyłącz akcję"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"Disabling this automated action will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""
"Wyłączenie tej automatycznej akcji pozwoli na kontynuowanie pracy\n"
"ale wszelkie dane utworzone po tym mogą być potencjalnie uszkodzone,\n"
"ponieważ efektywnie wyłączasz dostosowanie, które może ustawić\n"
"ważne i/lub wymagane pola."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range
msgid "Due Date In"
msgstr "Termin za"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range_type
msgid "Due type"
msgstr "Rodzaj terminu"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Edit action"
msgstr "Edytuj akcję"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__template_id
msgid "Email Template"
msgstr "Szablon wiadomości"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Email, followers or activities action types cannot be used when deleting "
"records."
msgstr ""
"Podczas usuwania rekordów nie można używać typów akcji email, obserwujący "
"czy aktywność."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__xml_id
msgid "External ID"
msgstr "Identyfikator zewnętrzny"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "Pola które aktywują się przy zmianie."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Form Modification based actions can only be used with code action type."
msgstr ""
"Akcje oparte na modyfikacji formularza mogą być używane tylko z typem akcji "
"code."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__groups_id
msgid "Groups"
msgstr "Grupy"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "Godziny"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "ID akcji, jeśli została zdefiniowana w pliku XML"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the action "
"rule."
msgstr ""
"Jeśli podane, to warunek musi być spełniony, aby reguła akcji została "
"wykonana."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record."
msgstr ""
"Jeśli podane, to warunek musi być spełniony, aby rekord został "
"zmodyfikowany."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "Ostatnie uruchomienie"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr "Najmniej Opóźniona Wiadomość"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__link_field_id
msgid "Link Field"
msgstr "Pole linku"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "Minuty"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "Model"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "Nazwa modelu"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__crud_model_id
msgid ""
"Model for record creation / update. Set this field only to specify a "
"different model than the base model."
msgstr ""
"Model do tworzenia / aktualizacji rekordów. Ustaw to pole tylko, aby "
"określić inny model niż model podstawowy."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the server action runs."
msgstr "Model, na którym działa akcja serwera."

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
msgid "Months"
msgstr "Miesiące"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_note
msgid "Note"
msgstr "Notatka"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Note that this action can be triggered up to %d minutes after its schedule."
msgstr ""
"Zauważ, że ta akcja może być uruchomiona do %d minut po jej zaplanowaniu."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "Wyzwalacz pól onchange"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On Creation"
msgstr "Przy tworzeniu"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On Creation & Update"
msgstr "Przy tworzeniu i zapisie"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On Deletion"
msgstr "Po usunięciu"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On Update"
msgstr "Przy zapisie"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__help
msgid ""
"Optional help text for the users with a description of the target view, such"
" as its usage and purpose."
msgstr ""
"Opcjonalny tekst dla użytkowników z opisem widoku docelowego, na przykład "
"jego użycie i cel."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__link_field_id
msgid ""
"Provide the field used to link the newly created record on the record used "
"by the server action."
msgstr ""
"Podaj pole używane do łączenia nowo utworzonego rekordu na rekordzie "
"używanego przez działanie serwera."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__code
msgid "Python Code"
msgstr "Kod Python"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_id
msgid "Responsible"
msgstr "Odpowiedzialny"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__mail_post_method
msgid "Send as"
msgstr "Wyślij jako"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__sequence
msgid "Sequence"
msgstr "Sekwencja"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "Akcja serwera"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_id
msgid "Server Actions"
msgstr "Akcje serwera"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__binding_model_id
msgid ""
"Setting a value makes this action available in the sidebar for the given "
"model."
msgstr ""
"Ustawienie wartości powoduje, że ta czynność jest dostępna na pasku bocznym "
"dla danego modelu."

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Setup a new automated automation"
msgstr "Skonfiguruj nową automatyzację"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr "Adresaci subskrypcji"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_summary
msgid "Summary"
msgstr "Podsumowanie"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_id
msgid "Target Model"
msgstr "Model docelowy"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_name
msgid "Target Model Name"
msgstr "Nazwa modelu docelowego"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"\"%(trigger_value)s\"  %(trigger_label)s może być użyty tylko z typem akcji "
"\"%(state_value)s\"."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The action will be triggered if and only if one of these fields is updated. "
"If empty, all fields are watched."
msgstr ""
"Akcja zostanie wywołana wtedy i tylko wtedy, gdy jedno z tych pól zostanie "
"zaktualizowane. Jeśli puste, wszystkie pola są obserwowane."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"The error occurred during the execution of the automated action\n"
"                \""
msgstr "Wystąpił błąd podczas wykonywania akcji automatycznej."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "Wyzwalacz"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "Data wyzwolenia"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Trigger Fields"
msgstr "Pola wyzwalające"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create a new Record': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)\n"
"- 'Send SMS Text Message': send SMS, log them on documents (SMS)"
msgstr ""
"Typ działania serwera. Dostępne są następujące wartości:\n"
"- 'Wykonaj kod Python': blok kodu python, który zostanie wykonany\n"
"- \"Utwórz nowy rekord\": utworzenie nowego rekordu z nowymi wartościami\n"
"- \"Aktualizacja rekordu\": aktualizacja wartości rekordu\n"
"- \"Wykonaj kilka działań\": zdefiniowanie akcji, która uruchamia kilka innych akcji serwera\n"
"- \"Wyślij Email': umieść wiadomość, notatkę lub wyślij email (Discuss)\n"
"- 'Dodaj followersów': dodawanie followersów do rekordu (dyskusja)\n"
"- Tworzenie następnej aktywności': tworzenie aktywności (Dyskusja)\n"
"- 'Wyślij wiadomość SMS': wyślij SMS, zaloguj je na dokumentach (SMS)"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "Użycie"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""
"Użyj \"Określony użytkownik\", aby zawsze przypisać tego samego użytkownika "
"do następnej czynności. Użyj \"Ogólny użytkownik z rekordu\", aby określić "
"nazwę pola użytkownika, którego chcesz wybrać w rekordzie."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "Stosuj kalendarz"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Use automated actions to automatically trigger actions for\n"
"                various screens. Example: a lead created by a specific user may\n"
"                be automatically set to a specific Sales Team, or an\n"
"                opportunity which still has status pending after 14 days might\n"
"                trigger an automatic reminder email."
msgstr ""
"Używaj automatycznych akcji do automatycznego wyzwalania akcji dla\n"
"różnych ekranów. Przykład: lead utworzony przez określonego użytkownika może\n"
"może zostać automatycznie przypisany do określonego zespołu sprzedaży, lub\n"
"okazja, która nadal ma status oczekującej po 14 dniach, może\n"
"wyzwolić automatyczną wiadomość e-mail z przypomnieniem."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_field_name
msgid "User field name"
msgstr "Nazwa pola użytkownika"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__fields_lines
msgid "Value Mapping"
msgstr "Mapowanie wartości"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Warning"
msgstr "Ostrzeżenie"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possible to use a "
"calendar to compute the date based on working days."
msgstr ""
"Przy obliczaniu warunku opartego na dniu można użyć kalendarza do obliczenia"
" daty na podstawie dni roboczych."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__sequence
msgid ""
"When dealing with multiple actions, the execution order is based on the "
"sequence. Low number means high priority."
msgstr ""
"W przypadku wielu akcji kolejność wykonywania zależy od sekwencji. Niski "
"numer oznacza wysoki priorytet."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"Kiedy warunek powinien zostać wywołany. \n"
"Jeśli jest obecny, zostanie sprawdzony przez schedulera. Jeśli pusty, zostanie sprawdzony przy tworzeniu i aktualizacji."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr ""
"Kiedy nie jest zaznaczone, ta zasada jest ukryta i nie będzie wykonywana."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__code
msgid ""
"Write Python code that the action will execute. Some variables are available"
" for use; help about python expression is given in the help tab."
msgstr ""
"Napisz kod w języku Python, który wykona akcję. Niektóre zmienne są dostępne"
" do użycia; pomoc na temat wyrażeń python znajduje się w zakładce pomocy."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"You can ask an administrator to disable or correct this automated action."
msgstr ""
"Możesz poprosić administratora o wyłączenie lub poprawienie tej "
"automatycznej akcji."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "You can disable this automated action or edit it to solve the issue."
msgstr ""
"Możesz wyłączyć tę automatyczną akcję lub edytować ją, aby rozwiązać "
"problem."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
"Nie można wysłać wiadomości e-mail, dodać obserwujących ani stworzyć "
"aktywności dla usuniętego rekordu. To po prostu nie działa."
