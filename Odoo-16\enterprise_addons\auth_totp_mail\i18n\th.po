# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Rasareeyar Lappiam, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_totp_mail
#: model:mail.template,body_html:auth_totp_mail.mail_template_totp_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"        <t t-out=\"user.name  or ''\"></t> requested you activate two-factor authentication to protect your account.<br><br>\n"
"        Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator.\n"
"\n"
"        <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Activate my two-factor Authentication\n"
"            </a>\n"
"        </p>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        เรียน <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"        <t t-out=\"user.name  or ''\"></t> ขอให้คุณเปิดใช้งานการรับรองความถูกต้องด้วย two-factor เพื่อปกป้องบัญชีของคุณ<br><br>\n"
"        การตรวจสอบสิทธิ์แบบ two-factor (\"2FA\") คือระบบการตรวจสอบสิทธิ์แบบสองชั้น\n"
"        อันแรกใช้รหัสผ่านของคุณและอันที่สองใช้รหัสที่คุณได้รับจากแอปมือถือโดยเฉพาะ\n"
"        แอปยอดนิยม ได้แก่ Authy, Google Authenticator หรือ Microsoft Authenticator\n"
"\n"
"        <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                เปิดใช้งานการตรวจสอบสิทธิ์แบบ two-factor ของฉัน\n"
"            </a>\n"
"        </p>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Account Security"
msgstr "ความปลอดภัยของบัญชี"

#. module: auth_totp_mail
#: model:mail.template,subject:auth_totp_mail.mail_template_totp_invite
msgid "Invitation to activate two-factor authentication on your Odoo account"
msgstr ""
"คำเชิญให้เปิดใช้งานการรับรองความถูกต้องแบบสองปัจจัยในบัญชี Odoo ของคุณ"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid ""
"Invitation to use two-factor authentication sent for the following user(s): "
"%s"
msgstr ""
"คำเชิญให้ใช้การรับรองความถูกต้องแบบสองปัจจัยที่ส่งถึงผู้ใช้ต่อไปนี้:%s"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.view_users_form
msgid "Invite to use 2FA"
msgstr "คำเชิญให้ใช้งาน2FA"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_invite_totp
msgid "Invite to use two-factor authentication"
msgstr "เชิญให้ใช้การรับรองความถูกต้องด้วยแบบสองปัจจัย"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.res_users_view_form
msgid "Name"
msgstr "ชื่อ"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_activate_two_factor_authentication
msgid "Open two-factor authentication configuration"
msgstr "เปิดการกำหนดค่าการรับรองความถูกต้องแบบสองปัจจัย"

#. module: auth_totp_mail
#: model:mail.template,name:auth_totp_mail.mail_template_totp_invite
msgid "Settings: 2Fa Invitation"
msgstr "การตั้งค่า: คำเชิญ 2Fa"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_res_users
msgid "User"
msgstr "ผู้ใช้"
