<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="autovacuum_job" model="ir.cron">
        <field name="name">Base: Auto-vacuum internal data</field>
        <field name="model_id" ref="model_ir_autovacuum"/>
        <field name="state">code</field>
        <field name="code">model._run_vacuum_cleaner()</field>
        <field name='interval_number'>1</field>
        <field name='interval_type'>days</field>
        <field name="numbercall">-1</field>
    </record>
</odoo>
