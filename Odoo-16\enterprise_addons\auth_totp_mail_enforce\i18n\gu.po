# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail_enforce
# 
# Translators:
# Qaidjo<PERSON> Barbhaya, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: Qaidjohar Barbhaya, 2023\n"
"Language-Team: Gujarati (https://app.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
".\n"
"                <br/>"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:mail.template,body_html:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    Dear <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"    <p>Someone is trying to log in into your account with a new device.</p>\n"
"    <ul>\n"
"        <t t-set=\"not_available\">N/A</t>\n"
"        <li>Location: <t t-out=\"ctx.get('location') or not_available\"></t></li>\n"
"        <li>Device: <t t-out=\"ctx.get('device') or not_available\"></t></li>\n"
"        <li>Browser: <t t-out=\"ctx.get('browser') or not_available\"></t></li>\n"
"        <li>IP address: <t t-out=\"ctx.get('ip') or not_available\"></t></li>\n"
"    </ul>\n"
"    <p>If this is you, please enter the following code to complete the login:</p>\n"
"    <t t-set=\"code_expiration\" t-value=\"object._get_totp_mail_code()\"></t>\n"
"    <t t-set=\"code\" t-value=\"code_expiration[0]\"></t>\n"
"    <t t-set=\"expiration\" t-value=\"code_expiration[1]\"></t>\n"
"    <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <span t-out=\"code\" style=\"background-color:#faf9fa; border: 1px solid #dad8de; padding: 8px 16px 8px 16px; font-size: 24px; color: #875A7B; border-radius: 5px;\"></span>\n"
"    </div>\n"
"    <small>Please note that this code expires in <t t-out=\"expiration\"></t>.</small>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        If you did NOT initiate this log-in,\n"
"        you should immediately change your password to ensure account security.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        We also strongly recommend enabling the two-factor authentication using an authenticator app to help secure your account.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"            Activate my two-factor authentication\n"
"        </a>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
"<i class=\"fa fa-envelope-o\"/>\n"
"                To login, enter below the six-digit authentication code just sent via email to"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__res_config_settings__auth_totp_policy__all_required
msgid "All users"
msgstr ""

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "Cannot send email: user %s has no email address."
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__auth_totp_rate_limit_log__limit_type__code_check
msgid "Code Checking"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_res_config_settings
msgid "Config Settings"
msgstr "Config Settings"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__create_uid
msgid "Created by"
msgstr "Created by"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__create_date
msgid "Created on"
msgstr "Created on"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__display_name
msgid "Display Name"
msgstr "Display Name"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__res_config_settings__auth_totp_policy__employee_required
msgid "Employees only"
msgstr ""

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.res_config_settings_view_form
msgid ""
"Enforce the two-factor authentication by email for employees or for all users\n"
"                                (including portal users) if they didn't enable any other two-factor authentication\n"
"                                method."
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_res_config_settings__auth_totp_enforce
msgid "Enforce two-factor authentication"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__id
msgid "ID"
msgstr "ID"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__ip
msgid "Ip"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log____last_update
msgid "Last Modified on"
msgstr "Last Modified on"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid "Learn More"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__limit_type
msgid "Limit Type"
msgstr ""

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid "Re-send email"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__scope
msgid "Scope"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__auth_totp_rate_limit_log__limit_type__send_email
msgid "Send Email"
msgstr "Send Email"

#. module: auth_totp_mail_enforce
#: model:mail.template,name:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid "Settings: 2Fa New Login"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_auth_totp_rate_limit_log
msgid "TOTP rate limit logs"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_res_config_settings__auth_totp_policy
msgid "Two-factor authentication enforcing policy"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_res_users
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__user_id
msgid "User"
msgstr "User"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "Verification failed, please double-check the 6-digit code"
msgstr ""

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
"We strongly recommend enabling the two-factor authentication using an authenticator app to help secure your account.\n"
"                <br/>"
msgstr ""

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "You reached the limit of authentication mails sent for your account"
msgstr ""

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "You reached the limit of code verifications for your account"
msgstr ""

#. module: auth_totp_mail_enforce
#: model:mail.template,subject:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid "Your two-factor authentication code"
msgstr ""
