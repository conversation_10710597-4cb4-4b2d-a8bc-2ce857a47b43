<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.ui.view" id="view_base_automation_form_resource">
        <field name="name">base.automation.form</field>
        <field name="model">base.automation</field>
        <field name="inherit_id" ref="base_automation.view_base_automation_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='trg_date_calendar_id']" position="before">
                <field name="trg_date_resource_field_id"
                    domain="[('model_id', '=', model_id), ('relation', '=', 'res.users'), ('ttype', 'in', ['many2one'])]"
                    attrs="{'invisible': ['|', ('trg_date_id','=',False), ('trg_date_range_type', '!=', 'day')]}"/>
            </xpath>
        </field>
    </record>
</odoo>
