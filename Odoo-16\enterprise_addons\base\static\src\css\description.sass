@charset "utf-8" 
@import "compass/css3"
@import "compass/css3/user-interface"

/**
 *  This CSS is for the html description of modules
 * TODO clean
 */

/* --------------------------------- * 
 *          STYLING CONTEXT          *
 * --------------------------------- */

/* --- Styling for the V8/Lato/White/Purple design --- */

$v8_title_font_color: rgb(51,51,51)
$v8_text_font_color:  rgb(100,100,100)
$v8_font_paragraph_color:   rgb(51,51,51)
$v8_body_color:   rgb(245,245,245)
$v8_bg_color:     rgb(255,255,255)
$v8_text_font_family:  'Open Sans','Helvetica',Sans
$v8_title_font_family:  'Lato','Open Sans','Helvetica',Sans
$v8_anchor_color: #6D57E0
$v8_anchor_visited_color: rgb(91, 40, 79)


.openerp .oe_form_sheet_width
  max-width: 960px

.o_web_client .o_form_view .oe_styling_v8 .container
  width: 100%

.openerp .oe_form .oe_styling_v8
  width: 100%
  padding: 0
  margin: 0
  font-family: $v8_text_font_family
  font-weight: 300
  color: $v8_text_font_color
  background: $v8_bg_color
  font-size: 16px

  .container
    width: 100%

  .oe_websiteonly
    display: none

  .oe_website_contents
    background: $v8_body_color
    padding-bottom: 1px

  b
    font-weight: 600
  a
    color: $v8_anchor_color
    text-decoration: none
  a:visited
    color: $v8_anchor_visited_color
  a:hover
    color: #0096EB

  .oe_title_font
    font-family: $v8_title_font_family


  .oe_page
    background: $v8_bg_color
    overflow: hidden
    +border-radius(1px)
    +box-shadow(0 1px 3px rgba(0,0,0,0.35))

  .oe_emph
    font-weight: 400

  .oe_dark
    overflow: hidden
    background: #FCFCFC
    +box-shadow(0px 5px 9px -7px rgba(0,0,255,0.5) inset, 0px -3px 9px -7px rgba(0,0,255,0.5) inset)

/* --------------------------------- * 
 *               LAYOUT              *
 * --------------------------------- */

/* ------ BASE GRID CONSTRUCTS ----- */

.oe_page
  margin: 0px auto 64px auto
  max-width: 100%


.oe_row
  width: 100%
  margin-top: 16px
  margin-bottom: 16px
  margin-left: auto
  margin-right: auto

.oe_row.oe_fit
  width: auto

.oe_clearfix:after, .oe_row:after
  content: "."
  display: block
  clear: both
  visibility: hidden
  line-height: 0
  height: 0

$oe_span12_width: 100%
$oe_span10_width: 83.33333333%
$oe_span9_width:  75%
$oe_span8_width:  66.66666667%
$oe_span6_width:  50%
$oe_span4_width:  33.33333333%
$oe_span3_width:  25%
$oe_span2_width:  16.66666667%

[class*='oe_span']
  float: left
  +box-sizing(border-box)
  padding: 0 16px
.oe_span12
  width: $oe_span12_width
.oe_span10
  width: $oe_span10_width
.oe_span9
  width: $oe_span9_width
.oe_span8
  width: $oe_span8_width
.oe_span6
  width: $oe_span6_width
.oe_span4
  width: $oe_span4_width
.oe_span3
  width: $oe_span3_width
.oe_span2
  width: $oe_span2_width

[class*='oe_span'].oe_fit
  padding-left: 0px !important
  padding-right: 0px !important

[class*='oe_span'].oe_right
  float: right

.oe_row.oe_flex
  [class*='oe_span']
    display: inline-block
    float: none
    vertical-align: top
    +box-sizing(border-box)
    padding: 0 16px
    width: auto
  .oe_span12
    max-width: $oe_span12_width
  .oe_span10
    max-width: $oe_span10_width
  .oe_span9
    max-width: $oe_span9_width
  .oe_span8
    max-width: $oe_span8_width
  .oe_span6
    max-width: $oe_span6_width
  .oe_span4
    max-width: $oe_span4_width
  .oe_span3
    max-width: $oe_span3_width
  .oe_span2
    max-width: $oe_span2_width

.oe_mb0
  margin-bottom: 0px !important
.oe_mb4
  margin-bottom: 4px !important
.oe_mb8
  margin-bottom: 8px !important
.oe_mb16
  margin-bottom: 16px !important
.oe_mb32
  margin-bottom: 32px !important
.oe_mb48
  margin-bottom: 48px !important
.oe_mb64
  margin-bottom: 64px !important

.oe_mt0
  margin-top: 0px !important
.oe_mt4
  margin-top: 4px !important
.oe_mt8
  margin-top: 8px !important
.oe_mt16
  margin-top: 16px !important
.oe_mt32
  margin-top: 32px !important
.oe_mt48
  margin-top: 48px !important
.oe_mt64
  margin-top: 64px !important

/* ------ GENERIC LAYOUT MODIFIERS ----- */

.oe_rightfit
  padding-right: 0px !important
.oe_leftfit
  padding-left: 0px !important
.oe_leftalign 
  text-align: left
.oe_rightalign
  text-align: right
.oe_centeralign
  text-align: center
.oe_centered
  margin-left: auto
  margin-right: auto
.oe_hidden
  display: none !important
  opacity: 0 !important
.oe_invisible
  visibility: hidden !important
.oe_transparent
  opacity: 0 !important

.oe_mb0
  margin-bottom: 0px !important
.oe_mb4
  margin-bottom: 4px !important
.oe_mb8
  margin-bottom: 8px !important
.oe_mb16
  margin-bottom: 16px !important
.oe_mb32
  margin-bottom: 32px !important
.oe_mb64
  margin-bottom: 64px !important

.oe_spaced
  margin-top: 32px 
  margin-bottom: 32px 
.oe_more_spaced
  margin-top: 64px 
  margin-bottom: 64px
.oe_padded
  padding-top: 16px 
  padding-bottom: 16px
.oe_more_padded
  padding-top: 32px
  padding-bottom: 32px

/* --------------------------------- * 
 *        WEBPAGE COMPONENTS         *
 * --------------------------------- */

/* ------ BUTTONS ----- */

.oe_button
  position: relative
  bottom: 0
  display: inline-block
  cursor: pointer
  +user-select(none)

.oe_styling_v8 .oe_button, .oe_styling_v8 a.oe_button
  padding: 8px 14px
  background: rgb(139, 114, 182)
  color: white
  +border-radius(2px)
  +box-shadow(0px 2px 0px rgb(175, 168, 204))
  +text-shadow(0px 1px 1px rgba(0,0,0, 0.44))
  border: solid 1px rgba(0,0,0,0.09)
  +transition-property((bottom, background))
  +transition-duration(250ms)
  &:hover
    background: rgb(139,91,221)
    color: white
  &:active
    background: rgb(51,51,51)
    bottom: -3px
  &.oe_big
    font-size: 24px
  &.oe_bigger
    font-size: 32px
  &.oe_small
    font-size: 13px
    padding: 2px 4px
    &:active
      bottom: -1px
  &.oe_medium
    padding: 5px 12px
    font-size: 16px
  &.oe_tacky
    background: rgb(255,68,68)
    +box-shadow(0px 2px 0px #eba8a8)
    &:hover
      background: rgb(255,16,16)
    &:active
      background: black
  &.oe_disabled
    background: rgb(200,200,200)
    +box-shadow(0px 2px 0px rgb(180,180,180))
    cursor: default
    &:hover
      background: rgb(200,200,200)
      +box-shadow(0px 2px 0px rgb(180,180,180))
    &:active
      background: rgb(200,200,200)
      bottom: 0px
      +box-shadow(0px 2px 0px rgb(180,180,180))

.oe_styling_v8.oe_styling_black .oe_button
  +box-shadow(0px 2px 0px rgb(70,53,85))

/* ------ FORMS ----- */

.oe_styling_v8
  .oe_input
    padding: 4px 7px
    border-radius: 3px
    border: solid 1px rgb(214,214,214)
    box-shadow: 0px 2px rgb(230,230,230)
    background: rgb(250,250,250)
    font-weight: 300
    outline: none
    @include transition( all 150ms linear )
    &:focus
      border: solid 1px rgb(150,150,150)
      box-shadow: 0px 2px rgb(210,210,210)

    &.oe_valid
      background: #F2FFEC
      border-color: rgb(177,235,182)
      box-shadow: 0px 2px rgb(225,248,225)
      color: rgb(15,97,15)

    &.oe_invalid
      background: rgb(255,242,242)
      border-color: #EBB1B1
      box-shadow: 0px 2px #F8E1E1
      color: #610F0F
    &.oe_big
      padding: 8px 14px
  .oe_input_label
    font-weight: 300
    font-size: 16px
    &.oe_big
      font-size: 20px

  /* FIXME: this is a quick hack for the release */ 

  .oe_textarea
    width: 300px
    height: 80px
  .oe_form_layout_table
    width: 100%
    td
      padding-bottom: 16px
      &:first-child
        text-align: right
        padding-right: 16px

/* ------ SLOGANS ----- */

.oe_styling_v8 
  .oe_slogan
    color: $v8_title_font_color
    font-family: $v8_title_font_family
    text-align: center
    margin-top: 32px
    margin-bottom: 32px

  h1.oe_slogan
    font-size: 64px
    font-weight: 900
    margin-top: 48px
    margin-bottom: 48px

  h2.oe_slogan
    font-size: 40px
    font-weight: 300

  h3.oe_slogan
    font-size: 26px
    font-weight: 300
    +opacity(0.5)

  h4.oe_slogan
    font-size: 24px
    font-weight: 300
  h4.oe_slogan:before, h4.oe_slogan:after
    margin: 0 20px
    content: ""
    display: inline-block
    width: 100px
    height: 0px
    border-top: solid 1px
    vertical-align: middle
    +opacity(0.3)

  h5.oe_slogan
    font-weight: 300
    //TODO

/* ------ QUOTES ----- */

.oe_quote
  margin: 8px
  padding: 16px
  background: rgba(0,0,0,0.02)
  border: solid 1px rgba(0,0,0,0.06)
  +border-radius(2px)

  .oe_q,q
    margin: 10px
    display: block
    font-style: italic
    text-align: center
    font-size: 20px
    color: rgb(78, 102, 231)
    &:before, &:after
      content: '\"'
      font-weight: 900
      +opacity(0.2)

  cite
    display: block
    font-style: normal
    margin-top: 16px
  
  .oe_photo
    float: left
    +border-radius(3px)
    margin-right: 16px

  .oe_author
    font-size: 20px
    padding-top: 6px
    color: rgb(141, 123, 172)

.oe_dark .oe_quote
  background: white
  border: 1px solid rgb(240,240,255)

/* ------ PICTURES ----- */

// display a picture in a span
.oe_picture
  display: block
  max-width: 84%
  max-height: 400px
  margin: 16px 8%

// style the picture like a screenshot
.oe_screenshot
  +border-radius(3px)
  +box-shadow(0px 3px 8px rgba(0,0,0,0.2))

// display a picture taking full width of a row
.oe_pic_ctr
  position: relative

.oe_pic_ctr > img.oe_picture
  width: 100%
  max-width: none
  max-height: none
  margin: 0

// styling of the picture's title
.oe_pic_ctr > .oe_title
  position: absolute
  top: 15px
  right: 38px

.oe_styling_v8 .oe_pic_ctr > .oe_title
  font-size: 64px
  color: white
  font-weight: 600
  margin: 0
  +text-shadow( 0px 2px 0px rgb(73, 73, 73), 0px 2px 5px rgba(0, 0, 0, 0.33), 0px 0px 60px rgba(0, 0, 0, 0.22))

/*  ----- Link Image with Footer ----- */
/* FIXME: Terrible CSS, rewrite this */

div.oe_demo
  position: relative
  border: 1px solid #dedede
  span.oe_demo_play
    top: 50%
    left: 50%
    width: 80px
    height: 60px
    margin-top: -30px
    margin-left: -40px
    display: block
    position: absolute
    background: url("../img/layout/play-button.png") no-repeat left top transparent
    pointer-events: none
  img
    max-width: 100%
    width: 100%
  div.oe_demo_footer
    position: absolute
    left: 0
    background-color: rgba(0,0,0,0.4)
    opacity: 0.85
    bottom: -1px
    width: 100%
    padding-top: 7px
    padding-bottom: 7px
    color: white
    font-size: 14px
    font-weight: bold
    border-bottom-left-radius: 3px
    border-bottom-right-radius: 3px
    pointer-events: none

div.oe_demo:hover
  span.oe_demo_play
    background: url("../img/layout/play-button-over.png") no-repeat left top transparent

/*  ----- SEPARATOR ----- */

.oe_styling_v8 .oe_container.oe_separator
  height: 64px
  margin-bottom: 16px
  @include background(linear-gradient(rgba(0,0,0,0),rgba(0,0,0,0.02)))
  +box-shadow(0px -3px 10px -5px rgba(0,0,0,0.1) inset)
  overflow-y: hidden

/*  ----- TABS -----  */

.oe_row_tabs
  text-align: center
  margin-top: 0px
  margin-bottom: 0px
  padding-top: 21px

.oe_row_tab
  position: relative
  min-width: 120px
  padding: 8px
  font-size: 20px
  display: inline-block
  margin: 0px -2px
  border-top-left-radius: 4px
  border-top-right-radius: 4px
  border: solid 1px rgba(0,0,0,0.1)
  border-bottom: none
  background: rgb(250,250,250)
  background-image: +linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.02))
  box-shadow: 0px -3px 10px -5px rgba(0,0,0,0.1) inset
  cursor: pointer
  @include transition(all 250ms linear)

.oe_row_tab:hover
  padding-bottom: 12px
  top: -4px
  background-color: white

.oe_row_tab.oe_active
  background-color: white
  background-image: none
  box-shadow: none
  border-top-color: rgb(130, 114, 182)
  border-top-width: 2px
  cursor: default

.oe_row_tab.oe_active:hover
  padding-bottom: 8px
  top: 0asx

/* ------ CALL TO ACTION ----- */

.oe_calltoaction
  height: 32px
  margin-top: -32px
  position: relative

