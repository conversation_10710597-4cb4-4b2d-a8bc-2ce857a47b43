<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <!-- ir.actions -->

        <record id="action_view" model="ir.ui.view">
            <field name="name">ir.actions.actions</field>
            <field name="model">ir.actions.actions</field>
            <field name="arch" type="xml">
                <form string="Action">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="type"/>
                    </group>
                </sheet>
                </form>
            </field>
        </record>
        <record id="action_view_tree" model="ir.ui.view">
            <field name="name">ir.actions.actions.tree</field>
            <field name="model">ir.actions.actions</field>
            <field name="arch" type="xml">
                <tree string="Action">
                    <field name="name"/>
                    <field name="type"/>
                </tree>
            </field>
        </record>
        <record id="action_view_search" model="ir.ui.view">
            <field name="name">ir.actions.actions.search</field>
            <field name="model">ir.actions.actions</field>
            <field name="arch" type="xml">
                <search string="Action">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('type','ilike',self)]" string="Action"/>
                </search>
            </field>
        </record>
        <record id="ir_sequence_actions" model="ir.actions.act_window">
            <field name="name">Actions</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ir.actions.actions</field>
            <field name="view_id" ref="action_view_tree"/>
            <field name="search_view_id" ref="action_view_search"/>
        </record>
        <menuitem id="next_id_6" name="Actions" parent="base.menu_custom" sequence="5"/>
        <menuitem action="ir_sequence_actions" id="menu_ir_sequence_actions" parent="next_id_6"/>

        <!-- ir.actions.report -->

        <record id="act_report_xml_view" model="ir.ui.view">
            <field name="name">ir.actions.report</field>
            <field name="model">ir.actions.report</field>
            <field name="arch" type="xml">
                <form string="Report">
                    <field name="binding_model_id" invisible="1"/>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="create_action" string="Add in the 'Print' menu" type="object"
                                    attrs="{'invisible':[('binding_model_id','!=',False)]}" icon="fa-plus-square"
                                    help="Display an option on related documents to print this report" class="oe_stat_button"/>
                            <button name="unlink_action" string="Remove from the 'Print' menu" type="object"
                                    attrs="{'invisible':[('binding_model_id','=',False)]}" icon="fa-minus-square"
                                    help="Remove the contextual action related to this report" class="oe_stat_button"/>
                            <button name="associated_view" string="QWeb views" type="object"
                                    attrs="{'invisible':[('report_type', 'not in', ['qweb-pdf', 'qweb-html', 'qweb-text'])]}" icon='fa-code'
                                    class="oe_stat_button"/>
                        </div>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="report_type"/>
                                <field name="paperformat_id"
                                       attrs="{'invisible':[('report_type','not in',['qweb-pdf'])]}"/>
                            </group>
                            <group>
                                <field name="model"/>
                                <field name="report_name"/>
                                <field name="print_report_name" />
                            </group>
                        </group>
                        <notebook>
                            <page name="security" string="Security">
                                <field name="groups_id"/>
                            </page>
                            <page name='advanced' string="Advanced Properties">
                                <group>
                                    <field name="attachment_use"/>
                                    <field name="attachment"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="act_report_xml_view_tree" model="ir.ui.view">
            <field name="name">ir.actions.report.tree</field>
            <field name="model">ir.actions.report</field>
            <field name="arch" type="xml">
                <tree string="Report xml">
                    <field name="name"/>
                    <field name="model"/>
                    <field name="type"/>
                    <field name="report_name"/>
                    <field name="report_type"/>
                    <field name="attachment"/>
                </tree>
            </field>
        </record>
        <record id="act_report_xml_search_view" model="ir.ui.view">
            <field name="name">ir.actions.report.search</field>
            <field name="model">ir.actions.report</field>
            <field name="arch" type="xml">
                <search string="Report Xml">
                    <field name="name"
                        filter_domain="['|', '|', '|', '|', ('name','ilike',self), ('model','ilike',self), ('type','ilike',self), ('report_name','ilike',self), ('report_type','ilike',self)]"
                        string="Report"/>
                    <field name="model" filter_domain="[('model','=', self)]" string="Model"/>
                    <group expand="0" string="Group By" colspan="4">
                        <filter string="Report Type" name="report_type" domain="[]" context="{'group_by':'report_type'}"/>
                        <filter string="Report Model" name="report_model" domain="[]" context="{'group_by':'model'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="ir_action_report" model="ir.actions.act_window">
            <field name="name">Reports</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ir.actions.report</field>
            <field name="view_id" ref="act_report_xml_view_tree"/>
            <field name="search_view_id" ref="act_report_xml_search_view"/>
        </record>
        <menuitem action="ir_action_report" id="menu_ir_action_report" parent="base.next_id_6"/>

        <!-- ir.actions.act_window -->

        <record id="view_window_action_tree" model="ir.ui.view">
            <field name="name">ir.actions.windows.tree</field>
            <field name="model">ir.actions.act_window</field>
            <field name="arch" type="xml">
                <tree string="Open Window">
                    <field name="name"/>
                    <field name="res_model"/>
                    <field name="view_id"/>
                    <field name="domain"/>
                    <field name="context"/>
                </tree>
            </field>
        </record>
        <record id="view_window_action_form" model="ir.ui.view">
            <field name="name">ir.actions.windows.form</field>
            <field name="model">ir.actions.act_window</field>
            <field name="arch" type="xml">
                <form string="Open a Window">
                <sheet>
                    <group>
                        <group name="main_details">
                            <field name="name"/>
                            <field name="xml_id" string="External ID"/>
                            <field name="res_model" string="Object"/>
                        </group>
                        <group name="action_details">
                            <field name="usage"/>
                            <field name="type" readonly="1"/>
                            <field name="target"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="general_settings" string="General Settings">
                            <group>
                                <group name="views" string="Views">
                                    <field name="view_mode"/>
                                    <field name="view_id"/>
                                    <field name="search_view_id"/>
                                </group>
                                <group name="filters" string="Filters">
                                    <field name="domain"/>
                                    <field name="context"/>
                                    <field name="limit"/>
                                    <field name="filter"/>
                                </group>
                            </group>
                            <group name="help" string="Help">
                                <field colspan="2" name="help" nolabel="1" class="oe-bordered-editor"/>
                            </group>
                            <group name="views_tree" string="Views">
                                <field colspan="2" name="view_ids" nolabel="1">
                                    <form string="Views">
                                        <group>
                                            <field name="sequence"/>
                                            <field name="view_mode"/>
                                            <field domain="[('type', '=', view_mode)]" name="view_id"/>
                                        </group>
                                    </form>
                                    <tree string="Views">
                                        <field name="sequence" widget="handle"/>
                                        <field name="view_mode"/>
                                        <field name="view_id"/>
                                    </tree>
                                </field>
                            </group>
                        </page>
                        <page string="Security" name="security">
                            <field name="groups_id"/>
                        </page>
                    </notebook>
                </sheet>
                </form>
            </field>
        </record>
        <record id="view_window_action_search" model="ir.ui.view">
            <field name="name">ir.actions.windows.search</field>
            <field name="model">ir.actions.act_window</field>
            <field name="arch" type="xml">
                <search string="Open a Window">
                    <field name="name" filter_domain="['|', ('name','ilike',self), ('res_model','ilike',self)]" string="Action"/>
                </search>
            </field>
        </record>
        <record id="ir_action_window" model="ir.actions.act_window">
            <field name="name">Window Actions</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ir.actions.act_window</field>
            <field name="search_view_id" ref="view_window_action_search"/>
        </record>
        <record id="ir_action_window_view1" model="ir.actions.act_window.view">
            <field eval="1" name="sequence"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="view_window_action_tree"/>
            <field name="act_window_id" ref="ir_action_window"/>
        </record>
        <record id="ir_action_window_view2" model="ir.actions.act_window.view">
            <field eval="2" name="sequence"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_window_action_form"/>
            <field name="act_window_id" ref="ir_action_window"/>
        </record>
        <menuitem action="ir_action_window" id="menu_ir_action_window" parent="base.next_id_6"/>

        <!-- ir.actions.server -->

        <record id="view_server_action_form" model="ir.ui.view">
            <field name="name">Server Action</field>
            <field name="model">ir.actions.server</field>
            <field name="arch" type="xml">
                <form string="Server Action">
                    <header>
                        <field name="binding_model_id" invisible="1"/>
                        <button name="create_action" string="Create Contextual Action" type="object"
                                class="btn-primary"
                                attrs="{'invisible':[('binding_model_id','!=',False)]}"
                                help="Display an option in the 'More' top-menu in order to run this action."/>
                        <button name="unlink_action" string="Remove Contextual Action" type="object"
                                attrs="{'invisible':[('binding_model_id','=',False)]}"
                                help="Remove 'More' top-menu contextual action related to this action"/>
                        <button name="run" string="Run" type="object"
                                class="btn-primary"
                                attrs="{'invisible':['|', ('model_id', '!=', %(base.model_ir_actions_server)s), ('state', '!=', 'code')]}"
                                help="Run this action manually."/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1><field name="name" placeholder="e.g. Update order quantity"/></h1>
                        </div>
                        <group name="action_wrapper">
                            <group name="action_content">
                                <field name="model_id" options="{'no_create': True}"/>
                                <field name="model_name" invisible="1"/>
                            </group>
                            <group>
                                <field name="state"/>
                                <field name="type" invisible="1"/>
                                <field name="crud_model_id"
                                    options="{'no_create': True}"
                                    attrs="{'invisible': [('state', '!=', 'object_create')], 'required': [('state', '=', 'object_create')]}"/>
                                <field name="crud_model_name" invisible="1"/>
                                <field name="link_field_id"
                                    domain="[('model_id', '=', model_id), ('relation', '=', crud_model_name),
                                    ('ttype', 'in', ['many2one', 'one2many', 'many2many'])]"
                                    options="{'no_create': True}"
                                    attrs="{'invisible': [('state', '!=', 'object_create')]}"
                                    context="{'default_model_id': model_id, 'default_relation': crud_model_name}"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Python Code" name='code'
                                    attrs="{'invisible': [('state', '!=', 'code')]}">
                                <field name="code" widget="ace" options="{'mode': 'python'}" placeholder="Enter Python code here. Help about Python expression is available in the help tab of this document."/>
                            </page>

                            <page string="Data to Write" name="page_object"
                                    attrs="{'invisible':[('state', 'not in', ['object_create', 'object_write'])]}">
                                <p attrs="{'invisible': [('model_id', '!=', False)]}">
                                    Please set the Model to Create before choosing values
                                </p>
                                <field name="fields_lines">
                                     <tree string="Field Mappings" editable="bottom">
                                        <field name="col1"
                                            options="{'no_create': True}"
                                            domain="['|', ('model_id', '=', parent.crud_model_id), ('model_id', '=', parent.model_id)]"/>
                                        <field name="evaluation_type"/>
                                        <field name="resource_ref"
                                            options="{'hide_model': True, 'no_create': True}"
                                            attrs="{'readonly': [('evaluation_type', '!=', 'reference')]}"/>
                                        <field name="value"
                                            attrs="{'readonly': [('evaluation_type', '=', 'reference')]}"
                                            options="{'no_create': True}"
                                            force_save="1"/>
                                    </tree>
                                </field>
                            </page>

                            <page name="security" string="Security">
                                <field name="groups_id"/>
                            </page>

                            <page string="Actions" name="actions"
                                    attrs="{'invisible': [('state', '!=', 'multi')]}">
                                <p class="oe_grey">
                                    If several child actions return an action, only the last one will be executed.
                                    This may happen when having server actions executing code that returns an action, or server actions returning a client action.
                                </p>
                                <field name="child_ids" domain="[('model_id', '=', model_id)]"/>
                            </page>

                            <page string="Help" name="help_info"
                                    attrs="{'invisible': [('state', '!=', 'code')]}">
                                <div style="margin-top: 4px;">
                                    <h3>Help with Python expressions</h3>
                                    <p>Various fields may use Python code or Python expressions. The following variables can be used:</p>
                                    <ul>
                                        <li><code>env</code>: Odoo Environment on which the action is triggered</li>
                                        <li><code>model</code>: Odoo Model of the record on which the action is triggered; is a void recordset</li>
                                        <li><code>record</code>: record on which the action is triggered; may be be void</li>
                                        <li><code>records</code>: recordset of all records on which the action is triggered in multi mode; may be void</li>
                                        <li><code>time</code>, <code>datetime</code>, <code>dateutil</code>, <code>timezone</code>: useful Python libraries</li>
                                        <li><code>log(message, level='info')</code>: logging function to record debug information in <code>ir.logging</code> table</li>
                                        <li><code>UserError</code>: Warning Exception to use with <code>raise</code></li>
                                        <li><code>Command</code>: x2Many commands namespace</li>
                                        <li>To return an action, assign: <code>action = {...}</code></li>
                                    </ul>
                                    <div attrs="{'invisible': [('state', '!=', 'code')]}">
                                        <p>Example of Python code</p>
<code style='white-space: pre-wrap'>
partner_name = record.name + '_code' \n
env['res.partner'].create({'name': partner_name})
</code>
                                    </div>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="view_server_action_tree" model="ir.ui.view">
            <field name="name">Server Actions</field>
            <field name="model">ir.actions.server</field>
            <field name="arch" type="xml">
                <tree string="Server Actions">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="state"/>
                    <field name="model_id"/>
                    <field name="usage"/>
                </tree>
            </field>
        </record>
        <record id="view_server_action_search" model="ir.ui.view">
            <field name="name">ir.actions.server.search</field>
            <field name="model">ir.actions.server</field>
            <field name="arch" type="xml">
                <search string="Server Actions">
                    <field name="name" string="Server Action"/>
                    <field name="model_id"/>
                    <field name="state"/>
                    <group expand="0" string="Group By" colspan="4" col="4">
                        <filter string="Action Type" name="action_type" domain="[]" context="{'group_by':'state'}"/>
                        <filter string="Model" name="model" domain="[]" context="{'group_by':'model_id'}"/>
                        <filter string="Usage" name="usage" domain="[]" context="{'group_by':'usage'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="action_server_action" model="ir.actions.act_window">
            <field name="name">Server Actions</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ir.actions.server</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_server_action_tree"/>
            <field name="search_view_id" ref="view_server_action_search"/>
            <field name="context">{'key':'server_action'}</field>
        </record>
        <menuitem action="action_server_action" id="menu_server_action" parent="base.next_id_6"/>

        <!-- ir.actions.todo -->

        <record id="ir_actions_todo_tree" model="ir.ui.view">
            <field name="model">ir.actions.todo</field>
            <field name="name">Config Wizard Steps</field>
            <field name="arch" type="xml">
                <tree string="Config Wizard Steps">
                    <field name="sequence" widget='handle'/>
                    <field name="action_id"/>
                    <field name="state" readonly="1"/>
                    <button name="action_launch" states="open" string="Launch" type="object" icon="fa-cogs" help="Launch Configuration Wizard"/>
                    <button name="action_open" states="done"
                            string="Todo" type="object" help="Set as Todo"
                            icon="fa-exchange"/>
                </tree>
            </field>
        </record>
        <record id="config_wizard_step_view_form" model="ir.ui.view">
            <field name="model">ir.actions.todo</field>
            <field name="name">Config Wizard Steps</field>
            <field name="arch" type="xml">
                <form string="Config Wizard Steps">
                  <header>
                        <button name="action_launch"
                            states="open" string="Launch"
                            type="object" icon="fa-cogs" class="oe_highlight" 
                            help="Launch Configuration Wizard"/>
                        <button name="action_open" states="done"
                            string="Set as Todo" type="object"
                            icon="fa-exchange" class="oe_highlight"/>
                        <field name="state" widget="statusbar" statusbar_visible="open,done" nolabel="1" readonly="1" />
                  </header>
                  <sheet>
                    <group col="4">
                        <field name="action_id"/>
                        <field name="sequence"/>
                    </group>
                  </sheet>
                </form>
            </field>
        </record>
        <record id="config_wizard_step_view_search" model="ir.ui.view">
            <field name="model">ir.actions.todo</field>
            <field name="name">ir.actions.todo.select</field>
            <field name="arch" type="xml">
                <search string="Search Actions">
                    <filter string="To Do" name="todo" domain=" [('state','=','open')]" help="Wizards to be Launched"/>
                    <field name="action_id"/>
                    <field name="state"/>
                </search>
            </field>
        </record>
        <record id="act_ir_actions_todo_form" model="ir.actions.act_window">
            <field name="name">Configuration Wizards</field>
            <field name="res_model">ir.actions.todo</field>
            <field name="view_id" ref="ir_actions_todo_tree"/>
            <field name="help">The configuration wizards are used to help you configure a new instance of Odoo. They are launched during the installation of new modules, but you can choose to restart some wizards manually from this menu.</field>
        </record>
        <menuitem id="menu_ir_actions_todo_form" action="act_ir_actions_todo_form" parent="base.next_id_6"/>
        <record id="action_run_ir_action_todo" model="ir.actions.server">
            <field name="name">Config: Run Remaining Action Todo</field>
            <field name="type">ir.actions.server</field>
            <field name="model_id" ref="model_res_config"/>
            <field name="state">code</field>
            <field name="code">
config = model.next() or {}
if config.get('type') not in ('ir.actions.act_window_close',):
    action = config
</field>
        </record>

</odoo>
