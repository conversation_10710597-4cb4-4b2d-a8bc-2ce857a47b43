# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"\"\n"
"                (ID:"
msgstr ""
"\"\n"
"                (ID:"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__help
msgid "Action Description"
msgstr "작업 설명"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Action Name"
msgstr "작업명"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__state
msgid "Action To Do"
msgstr "수행할 작업"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__type
msgid "Action Type"
msgstr "작업 유형"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "활성"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_type_id
msgid "Activity"
msgstr "활동"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_type
msgid "Activity User Type"
msgstr "활동 사용자 유형"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__partner_ids
msgid "Add Followers"
msgstr "팔로워 추가하기"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "적용"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "보관됨"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
msgid "Automated Action"
msgstr "자동 작업"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
msgid "Automated Actions"
msgstr "자동 작업"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation"
msgstr "자동화"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
#: model:ir.cron,cron_name:base_automation.ir_cron_data_base_automation_check
msgid "Base Action Rule: check and execute"
msgstr "기본 동작 규칙 : 확인 및 실행"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "Based on Form Modification"
msgstr "양식 수정에 기반"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on Timed Condition"
msgstr "시간 기반 조건"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "도메인 업데이트 전"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_model_id
msgid "Binding Model"
msgstr "바인딩 모델"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_type
msgid "Binding Type"
msgstr "바인딩 유형"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_view_types
msgid "Binding View Types"
msgstr "보기 유형을 묶기"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__child_ids
msgid "Child Actions"
msgstr "하위 작업"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__child_ids
msgid ""
"Child server actions that will be executed. Note that the last return "
"returned action value will be used as global return value."
msgstr "종속 서버 작업이 실행됩니다. 주의 : 전역 반환 값으로 사용할 반환된 작업 값을 마지막에 반환합니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__mail_post_method
msgid ""
"Choose method for email sending:\n"
"EMail: send directly emails\n"
"Post as Message: post on document and notify followers\n"
"Post as Note: log a note on document"
msgstr ""
"이메일 전송 방법 선택:\n"
"이메일: 이메일 직접 전송\n"
"메시지 게시: 문서에 게시하고 팔로워에게 안내\n"
"메모 게시: 문서에 메모 기록"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "작성자"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "작성일자"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
msgid "Days"
msgstr "일"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date.\n"
"        You can put a negative number if you need a delay before the\n"
"        trigger date, like sending a reminder 15 minutes before a meeting."
msgstr ""
"동작되는 날짜 이후에 연기시키기.\n"
"        동작일 이전에 날짜를 연기해야 하는 경우 음수를 입력하시면 되며, \n"
"        회의 15분 전에 알림 보내기와 같이 사용할 수 있습니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "트리거 날짜 이후 지연"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "지연 유형"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Disable Action"
msgstr "작업 비활성화"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"Disabling this automated action will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""
"자동화된 작업을 비활성화할 경우 작업 절차는 계속 진행할 수 있으나\n"
"                중요한 또는 필수 필드를 설정할 수 있는\n"
"                사용자 지정 양식을 비활성화하므로\n"
"                이 작업 이후에 생성된 모든 데이터는 손상될 가능성이 있습니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "표시명"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range
msgid "Due Date In"
msgstr "만기일"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range_type
msgid "Due type"
msgstr "만기 유형"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Edit action"
msgstr "작업 편집하기"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__template_id
msgid "Email Template"
msgstr "이메일 서식"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Email, followers or activities action types cannot be used when deleting "
"records."
msgstr "이메일, 팔로워 또는 활동 유형은 레코드를 삭제할 때 사용할 수 없습니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__xml_id
msgid "External ID"
msgstr "외부 ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "onchange를 트리거하는 필드."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Form Modification based actions can only be used with code action type."
msgstr "서식 수정 작업은 코드 작업 유형에서만 사용할 수 있습니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__groups_id
msgid "Groups"
msgstr "그룹"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "시간"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "XML 파일에 정의된 작업의 ID입니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the action "
"rule."
msgstr "작업 규칙을 실행하기 전에 이 조건을 만족해야 합니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record."
msgstr "레코드 갱신 전에 이 조건을 만족해야 합니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "최근 실행"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr "최소 지연 메시지"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__link_field_id
msgid "Link Field"
msgstr "링크 필드"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "분"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "모델"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "모델명"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__crud_model_id
msgid ""
"Model for record creation / update. Set this field only to specify a "
"different model than the base model."
msgstr "생성 / 업데이트된 모델을 기록하십시오. 이 필드를 설정하면 기본 모델과 다른 모델만 정의됩니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the server action runs."
msgstr "서버 동작이 실행되는 모델"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
msgid "Months"
msgstr "월"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_note
msgid "Note"
msgstr "노트"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Note that this action can be triggered up to %d minutes after its schedule."
msgstr "이 작업은 예약 후 약 %d분 후에 트리거됩니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "필드 변경을 위한 트리거"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On Creation"
msgstr "생성할 때"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On Creation & Update"
msgstr "생성 또는 갱신할 때"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On Deletion"
msgstr "삭제할 때"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On Update"
msgstr "갱신할 때"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__help
msgid ""
"Optional help text for the users with a description of the target view, such"
" as its usage and purpose."
msgstr "대상 보기에 대한 설명, 예를 들면 사용 및 용도가 있는 사용자를 위한 선택적 도움말."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__link_field_id
msgid ""
"Provide the field used to link the newly created record on the record used "
"by the server action."
msgstr "서버 작업에서 사용한 레코드에 새로 만든 레코드를 연결하는 데 사용되는 필드를 제공합니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__code
msgid "Python Code"
msgstr "파이썬 코드"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_id
msgid "Responsible"
msgstr "담당자"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__mail_post_method
msgid "Send as"
msgstr "다음으로 보내기"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__sequence
msgid "Sequence"
msgstr "순서"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "서버작업"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_id
msgid "Server Actions"
msgstr "서버 작업"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__binding_model_id
msgid ""
"Setting a value makes this action available in the sidebar for the given "
"model."
msgstr "값을 설정하면 주어진 모델의 메뉴에서 이 작업을 사용할 수 있습니다."

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Setup a new automated automation"
msgstr "새로운 자동화 시스템 구축"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr "구독 수신자"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_summary
msgid "Summary"
msgstr "요약"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_id
msgid "Target Model"
msgstr "대상 모델"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_name
msgid "Target Model Name"
msgstr "대상 모델명"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"\"%(trigger_value)s\" %(trigger_label)s는 \"%(state_value)s\" 작업 유형에만 사용할 수 "
"있습니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The action will be triggered if and only if one of these fields is updated. "
"If empty, all fields are watched."
msgstr "해당 필드 중에서 하나를 업데이트한 경우에만 작업을 동작시킵니다. 공란으로 둘 경우, 전체 필드를 확인합니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"The error occurred during the execution of the automated action\n"
"                \""
msgstr ""
"자동화된 작업을 실행하는 동안 오류가 발생했습니다.\n"
"                \""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "트리거"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "트리거된 날짜"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Trigger Fields"
msgstr "트리거 필드"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create a new Record': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)\n"
"- 'Send SMS Text Message': send SMS, log them on documents (SMS)"
msgstr ""
"서버 작업 유형입니다. 다음과 같은 값을 사용할 수 있습니다:\n"
"- 'Python 코드 실행': 실행할 Python 코드 블록입니다.\n"
"- '새로운 레코드 생성': 새로운 값을 가진 새 레코드를 생성합니다,\n"
"- '레코드 업데이트': 레코드 값을 업데이트합니다.\n"
"- '여러 작업 실행': 여러 가지의 다른 서버 작업을 동작시키는 작업을 정의합니다.\n"
"- '이메일 전송': 메시지나 메모를 게시하거나 이메일을 전송합니다 (메일 및 채팅 앱).\n"
"- '팔로워 추가': 레코드에 팔로워를 추가합니다 (메일 및 채팅 앱).\n"
"- '다음 작업 생성': 작업을 생성합니다 (메일 및 채팅 앱).\n"
"- 'SMS 문자 메시지 전송': SMS를 전송하고, 문서에 기록합니다 (SMS)."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "사용량"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""
"다음 작업에서 항상 동일한 사용자를 할당하려면 '특정 사용자'를 사용합니다. '레코드에서 일반 사용자'를 사용하여 레코드에서 선택할 "
"사용자의 필드 이름을 지정하십시오."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "일정표 사용"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Use automated actions to automatically trigger actions for\n"
"                various screens. Example: a lead created by a specific user may\n"
"                be automatically set to a specific Sales Team, or an\n"
"                opportunity which still has status pending after 14 days might\n"
"                trigger an automatic reminder email."
msgstr ""
"자동 작업을 사용하여 작업 자동 실행 다른 화면. \n"
"                예 : 특정 사용자가 만든 영업제안은 특정 영업팀에 자동으로 설정됩니다. \n"
"                14일이 지난 후에도 보류 상태인 영업제안은 자동 미리 알림 이메일을 트리거합니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_field_name
msgid "User field name"
msgstr "사용자 필드 이름"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__fields_lines
msgid "Value Mapping"
msgstr "매핑된 값"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Warning"
msgstr "경고"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possible to use a "
"calendar to compute the date based on working days."
msgstr "하루 기준으로 시간을 지정한 조건을 계산할 때는 작업일에 기초하여 날짜를 계산하는 달력을 사용하는 것이 가능합니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__sequence
msgid ""
"When dealing with multiple actions, the execution order is based on the "
"sequence. Low number means high priority."
msgstr "여러 작업을 처리 할 때 실행 순서는 시퀀스를 기반으로 합니다. 작은 숫자가 우선 순위가 높습니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"조건이 작동되어야 하는 경우.\n"
"                해당하는 경우, 스케줄러에서 확인합니다. 공란인 경우에는, 생성하거나 업데이트할 경우 확인합니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "선택을 취소하면 규칙은 숨겨지고 실행되지 않습니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__code
msgid ""
"Write Python code that the action will execute. Some variables are available"
" for use; help about python expression is given in the help tab."
msgstr ""
"작업을 실행할 파이썬 코드를 작성하십시오. 일부 변수는 사용할 수 있지만 파이썬 표현식에 대한 도움말은 도움말 옵션 탭에 있습니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"You can ask an administrator to disable or correct this automated action."
msgstr "관리자에게 이 자동 작업을 비활성화하거나 수정하도록 요청할 수 있습니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "You can disable this automated action or edit it to solve the issue."
msgstr "이 자동 작업을 비활성화하거나 편집하여 문제를 해결할 수 있습니다."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
"삭제된 레코드에 대해서는 이메일 전송이나 팔로워 추가, 활동 생성 등의 작업을 수행할 수 없습니다. 정상적으로 동작하지 않습니다."
