/**
 *  This CSS is for the html description of modules
 * TODO clean
 */
/* --------------------------------- *
 *          STYLING CONTEXT          *
 * --------------------------------- */
/* --- Styling for the V8/Lato/White/Purple design --- */
.openerp .oe_form_sheet_width {
  max-width: 960px;
}

.o_web_client .o_form_view .oe_styling_v8 .container {
  width: 100%;
}

.openerp .oe_form .oe_styling_v8 {
  width: 100%;
  padding: 0;
  margin: 0;
  font-family: "Open Sans", "Helvetica", Sans;
  font-weight: 300;
  color: #646464;
  background: white;
  font-size: 16px;
}
.openerp .oe_form .oe_styling_v8 .container {
  width: 100%;
}
.openerp .oe_form .oe_styling_v8 .oe_websiteonly {
  display: none;
}
.openerp .oe_form .oe_styling_v8 .oe_website_contents {
  background: whitesmoke;
  padding-bottom: 1px;
}
.openerp .oe_form .oe_styling_v8 b {
  font-weight: 600;
}
.openerp .oe_form .oe_styling_v8 a {
  color: #6D57E0;
  text-decoration: none;
}
.openerp .oe_form .oe_styling_v8 a:visited {
  color: #5b284f;
}
.openerp .oe_form .oe_styling_v8 a:hover {
  color: #0096EB;
}
.openerp .oe_form .oe_styling_v8 .oe_title_font {
  font-family: "Lato", "Open Sans", "Helvetica", Sans;
}
.openerp .oe_form .oe_styling_v8 .oe_page {
  background: white;
  overflow: hidden;
  -moz-border-radius: 1px;
  -webkit-border-radius: 1px;
  border-radius: 1px;
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35);
}
.openerp .oe_form .oe_styling_v8 .oe_emph {
  font-weight: 400;
}
.openerp .oe_form .oe_styling_v8 .oe_dark {
  overflow: hidden;
  background: #FCFCFC;
  -moz-box-shadow: 0px 5px 9px -7px rgba(0, 0, 255, 0.5) inset, 0px -3px 9px -7px rgba(0, 0, 255, 0.5) inset;
  -webkit-box-shadow: 0px 5px 9px -7px rgba(0, 0, 255, 0.5) inset, 0px -3px 9px -7px rgba(0, 0, 255, 0.5) inset;
  box-shadow: 0px 5px 9px -7px rgba(0, 0, 255, 0.5) inset, 0px -3px 9px -7px rgba(0, 0, 255, 0.5) inset;
}

/* --------------------------------- *
 *               LAYOUT              *
 * --------------------------------- */
/* ------ BASE GRID CONSTRUCTS ----- */
.oe_page {
  margin: 0px auto 64px auto;
  max-width: 100%;
}

.oe_row {
  width: 100%;
  margin-top: 16px;
  margin-bottom: 16px;
  margin-left: auto;
  margin-right: auto;
}

.oe_row.oe_fit {
  width: auto;
}

.oe_clearfix:after, .oe_row:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

[class*='oe_span'] {
  float: left;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 16px;
}

.oe_span12 {
  width: 100%;
}

.oe_span10 {
  width: 83.33333%;
}

.oe_span9 {
  width: 75%;
}

.oe_span8 {
  width: 66.66667%;
}

.oe_span6 {
  width: 50%;
}

.oe_span4 {
  width: 33.33333%;
}

.oe_span3 {
  width: 25%;
}

.oe_span2 {
  width: 16.66667%;
}

[class*='oe_span'].oe_fit {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

[class*='oe_span'].oe_right {
  float: right;
}

.oe_row.oe_flex [class*='oe_span'] {
  display: inline-block;
  float: none;
  vertical-align: top;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 16px;
  width: auto;
}
.oe_row.oe_flex .oe_span12 {
  max-width: 100%;
}
.oe_row.oe_flex .oe_span10 {
  max-width: 83.33333%;
}
.oe_row.oe_flex .oe_span9 {
  max-width: 75%;
}
.oe_row.oe_flex .oe_span8 {
  max-width: 66.66667%;
}
.oe_row.oe_flex .oe_span6 {
  max-width: 50%;
}
.oe_row.oe_flex .oe_span4 {
  max-width: 33.33333%;
}
.oe_row.oe_flex .oe_span3 {
  max-width: 25%;
}
.oe_row.oe_flex .oe_span2 {
  max-width: 16.66667%;
}

.oe_mb0 {
  margin-bottom: 0px !important;
}

.oe_mb4 {
  margin-bottom: 4px !important;
}

.oe_mb8 {
  margin-bottom: 8px !important;
}

.oe_mb16 {
  margin-bottom: 16px !important;
}

.oe_mb32 {
  margin-bottom: 32px !important;
}

.oe_mb48 {
  margin-bottom: 48px !important;
}

.oe_mb64 {
  margin-bottom: 64px !important;
}

.oe_mt0 {
  margin-top: 0px !important;
}

.oe_mt4 {
  margin-top: 4px !important;
}

.oe_mt8 {
  margin-top: 8px !important;
}

.oe_mt16 {
  margin-top: 16px !important;
}

.oe_mt32 {
  margin-top: 32px !important;
}

.oe_mt48 {
  margin-top: 48px !important;
}

.oe_mt64 {
  margin-top: 64px !important;
}

/* ------ GENERIC LAYOUT MODIFIERS ----- */
.oe_rightfit {
  padding-right: 0px !important;
}

.oe_leftfit {
  padding-left: 0px !important;
}

.oe_leftalign {
  text-align: left;
}

.oe_rightalign {
  text-align: right;
}

.oe_centeralign {
  text-align: center;
}

.oe_centered {
  margin-left: auto;
  margin-right: auto;
}

.oe_hidden {
  display: none !important;
  opacity: 0 !important;
}

.oe_invisible {
  visibility: hidden !important;
}

.oe_transparent {
  opacity: 0 !important;
}

.oe_mb0 {
  margin-bottom: 0px !important;
}

.oe_mb4 {
  margin-bottom: 4px !important;
}

.oe_mb8 {
  margin-bottom: 8px !important;
}

.oe_mb16 {
  margin-bottom: 16px !important;
}

.oe_mb32 {
  margin-bottom: 32px !important;
}

.oe_mb64 {
  margin-bottom: 64px !important;
}

.oe_spaced {
  margin-top: 32px;
  margin-bottom: 32px;
}

.oe_more_spaced {
  margin-top: 64px;
  margin-bottom: 64px;
}

.oe_padded {
  padding-top: 16px;
  padding-bottom: 16px;
}

.oe_more_padded {
  padding-top: 32px;
  padding-bottom: 32px;
}

/* --------------------------------- *
 *        WEBPAGE COMPONENTS         *
 * --------------------------------- */
/* ------ BUTTONS ----- */
.oe_button {
  position: relative;
  bottom: 0;
  display: inline-block;
  cursor: pointer;
  -moz-user-select: -moz-none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}

.oe_styling_v8 .oe_button, .oe_styling_v8 a.oe_button {
  padding: 8px 14px;
  background: #8b72b6;
  color: white;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
  -moz-box-shadow: 0px 2px 0px #afa8cc;
  -webkit-box-shadow: 0px 2px 0px #afa8cc;
  box-shadow: 0px 2px 0px #afa8cc;
  text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.44);
  border: solid 1px rgba(0, 0, 0, 0.09);
  -moz-transition-property: bottom, background;
  -o-transition-property: bottom, background;
  -webkit-transition-property: bottom, background;
  transition-property: bottom, background;
  -moz-transition-duration: 250ms;
  -o-transition-duration: 250ms;
  -webkit-transition-duration: 250ms;
  transition-duration: 250ms;
}
.oe_styling_v8 .oe_button:hover, .oe_styling_v8 a.oe_button:hover {
  background: #8b5bdd;
  color: white;
}
.oe_styling_v8 .oe_button:active, .oe_styling_v8 a.oe_button:active {
  background: #333333;
  bottom: -3px;
}
.oe_styling_v8 .oe_button.oe_big, .oe_styling_v8 a.oe_button.oe_big {
  font-size: 24px;
}
.oe_styling_v8 .oe_button.oe_bigger, .oe_styling_v8 a.oe_button.oe_bigger {
  font-size: 32px;
}
.oe_styling_v8 .oe_button.oe_small, .oe_styling_v8 a.oe_button.oe_small {
  font-size: 13px;
  padding: 2px 4px;
}
.oe_styling_v8 .oe_button.oe_small:active, .oe_styling_v8 a.oe_button.oe_small:active {
  bottom: -1px;
}
.oe_styling_v8 .oe_button.oe_medium, .oe_styling_v8 a.oe_button.oe_medium {
  padding: 5px 12px;
  font-size: 16px;
}
.oe_styling_v8 .oe_button.oe_tacky, .oe_styling_v8 a.oe_button.oe_tacky {
  background: #ff4444;
  -moz-box-shadow: 0px 2px 0px #eba8a8;
  -webkit-box-shadow: 0px 2px 0px #eba8a8;
  box-shadow: 0px 2px 0px #eba8a8;
}
.oe_styling_v8 .oe_button.oe_tacky:hover, .oe_styling_v8 a.oe_button.oe_tacky:hover {
  background: #ff1010;
}
.oe_styling_v8 .oe_button.oe_tacky:active, .oe_styling_v8 a.oe_button.oe_tacky:active {
  background: black;
}
.oe_styling_v8 .oe_button.oe_disabled, .oe_styling_v8 a.oe_button.oe_disabled {
  background: #c8c8c8;
  -moz-box-shadow: 0px 2px 0px #b4b4b4;
  -webkit-box-shadow: 0px 2px 0px #b4b4b4;
  box-shadow: 0px 2px 0px #b4b4b4;
  cursor: default;
}
.oe_styling_v8 .oe_button.oe_disabled:hover, .oe_styling_v8 a.oe_button.oe_disabled:hover {
  background: #c8c8c8;
  -moz-box-shadow: 0px 2px 0px #b4b4b4;
  -webkit-box-shadow: 0px 2px 0px #b4b4b4;
  box-shadow: 0px 2px 0px #b4b4b4;
}
.oe_styling_v8 .oe_button.oe_disabled:active, .oe_styling_v8 a.oe_button.oe_disabled:active {
  background: #c8c8c8;
  bottom: 0px;
  -moz-box-shadow: 0px 2px 0px #b4b4b4;
  -webkit-box-shadow: 0px 2px 0px #b4b4b4;
  box-shadow: 0px 2px 0px #b4b4b4;
}

.oe_styling_v8.oe_styling_black .oe_button {
  -moz-box-shadow: 0px 2px 0px #463555;
  -webkit-box-shadow: 0px 2px 0px #463555;
  box-shadow: 0px 2px 0px #463555;
}

/* ------ FORMS ----- */
.oe_styling_v8 {
  /* FIXME: this is a quick hack for the release */
}
.oe_styling_v8 .oe_input {
  padding: 4px 7px;
  border-radius: 3px;
  border: solid 1px #d6d6d6;
  box-shadow: 0px 2px #e6e6e6;
  background: #fafafa;
  font-weight: 300;
  outline: none;
  -moz-transition: all 150ms linear;
  -o-transition: all 150ms linear;
  -webkit-transition: all 150ms linear;
  transition: all 150ms linear;
}
.oe_styling_v8 .oe_input:focus {
  border: solid 1px #969696;
  box-shadow: 0px 2px #d2d2d2;
}
.oe_styling_v8 .oe_input.oe_valid {
  background: #F2FFEC;
  border-color: #b1ebb6;
  box-shadow: 0px 2px #e1f8e1;
  color: #0f610f;
}
.oe_styling_v8 .oe_input.oe_invalid {
  background: #fff2f2;
  border-color: #EBB1B1;
  box-shadow: 0px 2px #F8E1E1;
  color: #610F0F;
}
.oe_styling_v8 .oe_input.oe_big {
  padding: 8px 14px;
}
.oe_styling_v8 .oe_input_label {
  font-weight: 300;
  font-size: 16px;
}
.oe_styling_v8 .oe_input_label.oe_big {
  font-size: 20px;
}
.oe_styling_v8 .oe_textarea {
  width: 300px;
  height: 80px;
}
.oe_styling_v8 .oe_form_layout_table {
  width: 100%;
}
.oe_styling_v8 .oe_form_layout_table td {
  padding-bottom: 16px;
}
.oe_styling_v8 .oe_form_layout_table td:first-child {
  text-align: right;
  padding-right: 16px;
}

/* ------ SLOGANS ----- */
.oe_styling_v8 .oe_slogan {
  color: #333333;
  font-family: "Lato", "Open Sans", "Helvetica", Sans;
  text-align: center;
  margin-top: 32px;
  margin-bottom: 32px;
}
.oe_styling_v8 h1.oe_slogan {
  font-size: 64px;
  font-weight: 900;
  margin-top: 48px;
  margin-bottom: 48px;
}
.oe_styling_v8 h2.oe_slogan {
  font-size: 40px;
  font-weight: 300;
}
.oe_styling_v8 h3.oe_slogan {
  font-size: 26px;
  font-weight: 300;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50);
  opacity: 0.5;
}
.oe_styling_v8 h4.oe_slogan {
  font-size: 24px;
  font-weight: 300;
}
.oe_styling_v8 h4.oe_slogan:before, .oe_styling_v8 h4.oe_slogan:after {
  margin: 0 20px;
  content: "";
  display: inline-block;
  width: 100px;
  height: 0px;
  border-top: solid 1px;
  vertical-align: middle;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=30);
  opacity: 0.3;
}
.oe_styling_v8 h5.oe_slogan {
  font-weight: 300;
}

/* ------ QUOTES ----- */
.oe_quote {
  margin: 8px;
  padding: 16px;
  background: rgba(0, 0, 0, 0.02);
  border: solid 1px rgba(0, 0, 0, 0.06);
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.oe_quote .oe_q, .oe_quote q {
  margin: 10px;
  display: block;
  font-style: italic;
  text-align: center;
  font-size: 20px;
  color: #4e66e7;
}
.oe_quote .oe_q:before, .oe_quote .oe_q:after, .oe_quote q:before, .oe_quote q:after {
  content: '"';
  font-weight: 900;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=20);
  opacity: 0.2;
}
.oe_quote cite {
  display: block;
  font-style: normal;
  margin-top: 16px;
}
.oe_quote .oe_photo {
  float: left;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  margin-right: 16px;
}
.oe_quote .oe_author {
  font-size: 20px;
  padding-top: 6px;
  color: #8d7bac;
}

.oe_dark .oe_quote {
  background: white;
  border: 1px solid #f0f0ff;
}

/* ------ PICTURES ----- */
.oe_picture {
  display: block;
  max-width: 84%;
  max-height: 400px;
  margin: 16px 8%;
}

.oe_screenshot {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  -moz-box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2);
}

.oe_pic_ctr {
  position: relative;
}

.oe_pic_ctr > img.oe_picture {
  width: 100%;
  max-width: none;
  max-height: none;
  margin: 0;
}

.oe_pic_ctr > .oe_title {
  position: absolute;
  top: 15px;
  right: 38px;
}

.oe_styling_v8 .oe_pic_ctr > .oe_title {
  font-size: 64px;
  color: white;
  font-weight: 600;
  margin: 0;
  text-shadow: 0px 2px 0px #494949, 0px 2px 5px rgba(0, 0, 0, 0.33), 0px 0px 60px rgba(0, 0, 0, 0.22);
}

/*  ----- Link Image with Footer ----- */
/* FIXME: Terrible CSS, rewrite this */
div.oe_demo {
  position: relative;
  border: 1px solid #dedede;
}
div.oe_demo span.oe_demo_play {
  top: 50%;
  left: 50%;
  width: 80px;
  height: 60px;
  margin-top: -30px;
  margin-left: -40px;
  display: block;
  position: absolute;
  background: url("../img/layout/play-button.png") no-repeat left top transparent;
  pointer-events: none;
}
div.oe_demo img {
  max-width: 100%;
  width: 100%;
}
div.oe_demo div.oe_demo_footer {
  position: absolute;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0.85;
  bottom: -1px;
  width: 100%;
  padding-top: 7px;
  padding-bottom: 7px;
  color: white;
  font-size: 14px;
  font-weight: bold;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  pointer-events: none;
}

div.oe_demo:hover span.oe_demo_play {
  background: url("../img/layout/play-button-over.png") no-repeat left top transparent;
}

/*  ----- SEPARATOR ----- */
.oe_styling_v8 .oe_container.oe_separator {
  height: 64px;
  margin-bottom: 16px;
  background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiBncmFkaWVudFVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgeDE9IjAuNSIgeTE9IjAuMCIgeDI9IjAuNSIgeTI9IjEuMCI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIwLjAiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwMDAwMDAiIHN0b3Atb3BhY2l0eT0iMC4wMiIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+IA==');
  background: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, rgba(0, 0, 0, 0)), color-stop(100%, rgba(0, 0, 0, 0.02)));
  background: -moz-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02));
  background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02));
  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02));
  -moz-box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset;
  -webkit-box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset;
  box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset;
  overflow-y: hidden;
}

/*  ----- TABS -----  */
.oe_row_tabs {
  text-align: center;
  margin-top: 0px;
  margin-bottom: 0px;
  padding-top: 21px;
}

.oe_row_tab {
  position: relative;
  min-width: 120px;
  padding: 8px;
  font-size: 20px;
  display: inline-block;
  margin: 0px -2px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border: solid 1px rgba(0, 0, 0, 0.1);
  border-bottom: none;
  background: #fafafa;
  background-image: +linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02));
  box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset;
  cursor: pointer;
  -moz-transition: all 250ms linear;
  -o-transition: all 250ms linear;
  -webkit-transition: all 250ms linear;
  transition: all 250ms linear;
}

.oe_row_tab:hover {
  padding-bottom: 12px;
  top: -4px;
  background-color: white;
}

.oe_row_tab.oe_active {
  background-color: white;
  background-image: none;
  box-shadow: none;
  border-top-color: #8272b6;
  border-top-width: 2px;
  cursor: default;
}

.oe_row_tab.oe_active:hover {
  padding-bottom: 8px;
  top: 0asx;
}

/* ------ CALL TO ACTION ----- */
.oe_calltoaction {
  height: 32px;
  margin-top: -32px;
  position: relative;
}
