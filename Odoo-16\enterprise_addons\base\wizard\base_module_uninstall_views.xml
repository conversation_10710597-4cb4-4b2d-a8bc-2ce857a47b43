<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_base_module_uninstall" model="ir.ui.view">
            <field name="name">Uninstall module</field>
            <field name="model">base.module.uninstall</field>
            <field name="arch" type="xml">
                <form string="Uninstall module">
                    <div class="alert alert-warning oe_button_box" role="alert">
                        <p class="mt-3">
                            Uninstalling modules can be risky, we recommend you to try it on a duplicate or test database first.
                        </p>
                    </div>
                    <field name="module_id" invisible="1"/>
                    <div class="d-flex bd-highlight">
                        <div class="me-auto p-2 bd-highlight"><h3>Apps to Uninstall</h3></div>
                        <div class="p-2 bd-highlight"><field name="show_all"/> Show All</div>
                    </div>
                    <field name="module_ids" mode="kanban" class="o_modules_field">
                        <kanban create="false" class="o_modules_kanban">
                            <field name="icon"/>
                            <field name="state"/>
                            <field name="summary"/>
                                <templates>
                                    <t t-name="kanban-box">
                                        <div class="oe_module_vignette">
                                            <t t-set="installed" t-value="record.state.raw_value == 'installed'" />
                                            <img t-attf-src="#{record.icon.value}" class="oe_module_icon" alt="Icon" />
                                            <div class="oe_module_desc" t-att-title="record.shortdesc.value">
                                                <h4 class="o_kanban_record_title">
                                                    <field name="shortdesc" />&amp;nbsp;
                                                </h4>
                                                <p class="oe_module_name">
                                                    <field groups="!base.group_no_one" name="summary" />
                                                    <code groups="base.group_no_one">
                                                        <field name="name" /></code>
                                                </p>
                                            </div>
                                        </div>
                                    </t>
                                </templates>
                        </kanban>
                    </field>
                    <h3>Documents to Delete</h3>
                    <field name="model_ids" string="Models" nolabel="1">
                        <tree string="Models">
                            <field name="name" string="Document"/>
                            <field name="count"/>
                        </tree>
                    </field>
                    <footer>
                        <button string="Uninstall" class="btn-secondary" type="object" name="action_uninstall" data-hotkey="q"/>
                        <button string="Discard" class="btn-primary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

    </data>
</odoo>
